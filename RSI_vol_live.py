
import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime
import getpass

# --- Strategy parameters ---
symbols = [
    "Volatility 30 (1s) Index",
    "Volatility 10 (1s) Index",
    "Volatility 100 (1s) Index",
    "Volatility 75 (1s) Index"
]
lot_size_sequence = [0.7, 1.2, 1.5]
lot_size_index = 0
current_lot_size = lot_size_sequence[lot_size_index]
take_profit = 4.3
stop_loss = 3.5
active_position = None
last_signal = None

# --- KC Trader Header and Strategy Description ---
def print_header_and_strategy():
    print("\nKC Trader Terminal - Live Trading Dashboard")
    print("Trading is not about being right. It's about being prepared.")
    print("Success in trading is not a matter of chance, but of discipline and patience.")
    print("Welcome to KC Trader!\n")
    print("Strategy: RSI + EMA Trend Filter")
    print("Symbols: Volatility 30 (1s), 10 (1s), 100 (1s), 75 (1s) Index")
    print("Entry: RSI cross (M1) + EMA trend + M5 RSI filter")
    print("  - Long: RSI crosses above 30, M5 RSI > 50, price < EMA")
    print("  - Short: RSI crosses below 70, M5 RSI < 50, price > EMA")
    print("Exit: Take Profit (4.3), Stop Loss (3.5)")
    print("Lot Sizing: Adaptive (0.7, 1.2, 1.5) based on last trade result")
    print("Live PnL, Capital, History, Performance tracked and displayed\n")

def password_prompt():
    print("\nEnter password to start KC Trader:")
    pwd = getpass.getpass("Password: ")
    if pwd != "KC16":
        print("Incorrect password. Exiting.")
        exit()
    print("Access granted. Starting live trading...\n")
    time.sleep(1)

# Helper to calculate RSI
def calculate_rsi(price_series, rsi_period=7):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta < 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

# Get latest M1 data for a symbol
def get_latest_data(symbol, bars=100):
    # Fetch a large window of M1 and M5 bars for proper indicator calculation and alignment
    rates_m1 = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, 500)
    rates_m5 = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, 500)
    df_m1 = pd.DataFrame(rates_m1)
    df_m1['time'] = pd.to_datetime(df_m1['time'], unit='s')
    df_m5 = pd.DataFrame(rates_m5)
    df_m5['time'] = pd.to_datetime(df_m5['time'], unit='s')
    df_m1['RSI_lower'] = calculate_rsi(df_m1['close'], rsi_period=7)
    df_m5['RSI_higher'] = calculate_rsi(df_m5['close'], rsi_period=7)
    df_m5['M5_time'] = df_m5['time']
    df_m1 = pd.merge_asof(
        df_m1.sort_values('time'),
        df_m5[['time', 'RSI_higher', 'M5_time']].sort_values('time'),
        on='time',
        direction='backward'
    )
    df_m1['EMA15_lower'] = df_m1['close'].ewm(span=50, adjust=False).mean()
    return df_m1

# Send order (entry)
def send_order(symbol, direction, lot, price):
    order_type = mt5.ORDER_TYPE_BUY if direction == 'long' else mt5.ORDER_TYPE_SELL
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot,
        "type": order_type,
        "price": price,
        "deviation": 10,
        "magic": 123456,
        "comment": "RSI live strategy",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }
    result = mt5.order_send(request)
    return result

# Close position by ticket
def close_position(ticket):
    position = mt5.positions_get(ticket=ticket)
    if position:
        symbol = position[0].symbol
        volume = position[0].volume
        direction = position[0].type
        order_type = mt5.ORDER_TYPE_SELL if direction == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
        price = mt5.symbol_info_tick(symbol).bid if order_type == mt5.ORDER_TYPE_SELL else mt5.symbol_info_tick(symbol).ask
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": order_type,
            "position": ticket,
            "price": price,
            "deviation": 10,
            "magic": 123456,
            "comment": "RSI live exit",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        result = mt5.order_send(request)
        return result
    return None

# --- Trading Dashboard ---
def display_dashboard(active_position, capital, history, performance):
    from rich.panel import Panel
    from rich.layout import Layout
    from rich import box
    import pytz
    now = datetime.now(pytz.UTC).strftime('%Y-%m-%d %H:%M:%S UTC')

    layout = Layout()
    layout.split_column(
        Layout(name="header", size=5),
        Layout(name="main", ratio=2),
        Layout(name="footer", size=8),
        Layout(name="indicators", size=10),
        Layout(name="update", size=2)
    )

    # Header with big KC Trader and update time
    header_panel = Panel(
        f"[bold white on black]\nKC TRADER\n[/bold white on black]\n[bold cyan]Live Trading Dashboard[/bold cyan]",
        style="bold white on black",
        box=box.ROUNDED,
        padding=(1,2)
    )
    layout["header"].update(header_panel)

    # Open Position Table
    pos_table = Table(title="Open Position", style="bold green", box=box.ROUNDED)
    pos_table.add_column("Symbol", justify="center")
    pos_table.add_column("Direction", justify="center")
    pos_table.add_column("Entry Price", justify="center")
    pos_table.add_column("Current Price", justify="center")
    pos_table.add_column("PnL", justify="center")
    pos_table.add_column("Time", justify="center")
    if active_position:
        symbol = active_position['symbol']
        direction = active_position['direction']
        entry_price = active_position['entry_price']
        lot = active_position.get('lot', current_lot_size)
        ticket = active_position['ticket']
        position_info = mt5.positions_get(ticket=ticket)
        if position_info:
            pos = position_info[0]
            current_price = pos.price_current
            pnl_value = round(pos.profit, 2)
            pos_table.add_row(
                symbol, direction, f"{entry_price:.2f}", f"{current_price:.2f}", f"{pnl_value:.2f}", now
            )
        else:
            pos_table.add_row(symbol, direction, f"{entry_price:.2f}", "-", "-", now)
    else:
        pos_table.add_row("-", "-", "-", "-", "-", now)
    layout["main"].update(Panel(pos_table, border_style="green", box=box.ROUNDED))

    # History Table
    hist_table = Table(title="Recent Trades", style="bold yellow", box=box.ROUNDED)
    hist_table.add_column("Time", justify="center")
    hist_table.add_column("Symbol", justify="center")
    hist_table.add_column("Dir", justify="center")
    hist_table.add_column("Entry", justify="center")
    hist_table.add_column("Exit", justify="center")
    hist_table.add_column("PnL", justify="center")
    for h in history[-8:]:
        hist_table.add_row(*h)
    layout["footer"].update(Panel(hist_table, border_style="yellow", box=box.ROUNDED))

    # Performance Table
    perf_table = Table(title="Performance", style="bold magenta", box=box.ROUNDED)
    perf_table.add_column("Capital", justify="center")
    perf_table.add_column("Net PnL", justify="center")
    perf_table.add_column("Profitable Trades", justify="center")
    perf_table.add_column("Loss Trades", justify="center")
    perf_table.add_row(
        f"{capital:.2f}",
        f"{performance['net_pnl']:.2f}",
        str(performance['profit_count']),
        str(performance['loss_count'])
    )

    # RSI/EMA Indicator Table with True/False for each condition
    indicator_table = Table(title="RSI & EMA Status", style="bold cyan", box=box.ROUNDED)
    indicator_table.add_column("Symbol", justify="center")
    indicator_table.add_column("Time", justify="center")
    indicator_table.add_column("RSI_lower", justify="center")
    indicator_table.add_column("RSI_higher", justify="center")
    indicator_table.add_column("EMA15_lower", justify="center")
    indicator_table.add_column("Close", justify="center")
    indicator_table.add_column("RSI Cross", justify="center")
    indicator_table.add_column("M5 RSI Filter", justify="center")
    indicator_table.add_column("EMA Filter", justify="center")
    indicator_table.add_column("Entry Signal?", justify="center")
    for symbol in symbols:
        try:
            df = get_latest_data(symbol)
            # Use the current (last) bar for dashboard
            curr_bar = df.iloc[-1]
            prev_bar = df.iloc[-2] if len(df) > 1 else curr_bar
            rsi_lower = f"{curr_bar['RSI_lower']:.2f}"
            rsi_higher = f"{curr_bar['RSI_higher']:.2f}"
            ema15_lower = f"{curr_bar['EMA15_lower']:.2f}"
            close = f"{curr_bar['close']:.2f}"
            prev_rsi_lower = prev_bar['RSI_lower']
            time_str = str(curr_bar['time']) if 'time' in curr_bar and not pd.isna(curr_bar['time']) else "-"
            # Long entry conditions
            rsi_cross_long = prev_rsi_lower < 30 and curr_bar['RSI_lower'] >= 30
            m5_rsi_long = curr_bar['RSI_higher'] > 50
            ema_long = curr_bar['close'] < curr_bar['EMA15_lower']
            # Short entry conditions
            rsi_cross_short = prev_rsi_lower > 70 and curr_bar['RSI_lower'] <= 70
            m5_rsi_short = curr_bar['RSI_higher'] < 50
            ema_short = curr_bar['close'] > curr_bar['EMA15_lower']
            entry_long = rsi_cross_long and m5_rsi_long and ema_long
            entry_short = rsi_cross_short and m5_rsi_short and ema_short
            entry_signal = "Long" if entry_long else ("Short" if entry_short else "-")
            # Show which set of conditions matched
            if entry_long or (not entry_short):
                rsi_cross = str(rsi_cross_long)
                m5_rsi = str(m5_rsi_long)
                ema = str(ema_long)
            else:
                rsi_cross = str(rsi_cross_short)
                m5_rsi = str(m5_rsi_short)
                ema = str(ema_short)
            indicator_table.add_row(
                symbol, time_str, rsi_lower, rsi_higher, ema15_lower, close,
                rsi_cross, m5_rsi, ema, entry_signal
            )
        except Exception:
            indicator_table.add_row(symbol, "-", "-", "-", "-", "-", "-", "-", "-", "-", "-", "-")
    layout["indicators"].update(Panel(indicator_table, border_style="cyan", box=box.ROUNDED))

    # Update time panel
    update_panel = Panel(f"Last Update: [bold green]{now}[/bold green]", border_style="white", box=box.ROUNDED)
    layout["update"].update(update_panel)

    # Compose dashboard
    console.clear()
    console.print(layout)


def main():
    global current_lot_size, lot_size_index
    print_header_and_strategy()
    password_prompt()
    if not mt5.initialize():
        print("MT5 initialize() failed")
        return
    print("Live trading started.")

    # Use a dict to track open positions per symbol
    active_positions = {}
    last_checked_bar_time = {symbol: None for symbol in symbols}
    history = []
    performance = {"net_pnl": 0.0, "profit_count": 0, "loss_count": 0}
    # Initialize capital before loop
    account_info = mt5.account_info()
    capital = account_info.balance if account_info else 10000.0
    import time
    from datetime import datetime, timedelta
    while True:
        # Wait until the start of the next minute
        now = datetime.now()
        next_minute = (now + timedelta(minutes=1)).replace(second=0, microsecond=0)
        sleep_seconds = (next_minute - now).total_seconds()
        time.sleep(sleep_seconds)

        for symbol in symbols:
            # ...existing code for signal detection and trading logic...
            rates_m1 = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, 100)
            rates_m5 = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, 101)
            df_m5 = pd.DataFrame(rates_m5) if rates_m5 is not None else pd.DataFrame()
            if rates_m5 is None or len(df_m5) == 0:
                pass
            else:
                df_m5['time'] = pd.to_datetime(df_m5['time'], unit='s')
                df_m5 = df_m5.iloc[:-1] if len(df_m5) > 0 else df_m5
            if rates_m1 is None or rates_m5 is None or len(rates_m1) < 3 or len(df_m5) < 3:
                continue
            df_m1 = pd.DataFrame(rates_m1)
            df_m1['time'] = pd.to_datetime(df_m1['time'], unit='s')
            df_m5['RSI_higher'] = calculate_rsi(df_m5['close'], rsi_period=7)
            df_m5['M5_time'] = df_m5['time']
            df_m1['RSI_lower'] = calculate_rsi(df_m1['close'], rsi_period=7)
            df_m1 = pd.merge_asof(
                df_m1.sort_values('time'),
                df_m5[['time', 'RSI_higher', 'M5_time']].sort_values('time'),
                on='time',
                direction='backward'
            )
            df_m1['EMA15_lower'] = df_m1['close'].ewm(span=50, adjust=False).mean()
            if len(df_m1) < 3:
                continue
            prev_bar = df_m1.iloc[-3]
            signal_bar = df_m1.iloc[-2]
            entry_bar = df_m1.iloc[-1]
            if last_checked_bar_time[symbol] == signal_bar['time']:
                continue
            last_checked_bar_time[symbol] = signal_bar['time']
            lower_close_price = signal_bar['close']
            prev_rsi_lower = prev_bar['RSI_lower']
            curr_rsi_lower = signal_bar['RSI_lower']
            curr_rsi_higher = signal_bar['RSI_higher']
            curr_ema15_lower = signal_bar['EMA15_lower']
            is_long_entry = (
                prev_rsi_lower < 30 and curr_rsi_lower >= 30 and
                curr_rsi_higher > 45 and lower_close_price < curr_ema15_lower
            )
            is_short_entry = (
                prev_rsi_lower > 70 and curr_rsi_lower <= 70 and
                curr_rsi_higher < 55 and lower_close_price > curr_ema15_lower
            )
            # Only open a new position if no open position for this symbol
            if symbol not in active_positions and (is_long_entry or is_short_entry):
                direction = "long" if is_long_entry else "short"
                tick = mt5.symbol_info_tick(symbol)
                entry_price = tick.ask if direction == 'long' else tick.bid
                result = send_order(symbol, direction, current_lot_size, entry_price)
                tick_after = mt5.symbol_info_tick(symbol)
                actual_entry_price = tick_after.ask if direction == 'long' else tick_after.bid
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    active_positions[symbol] = {
                        "ticket": result.order,
                        "symbol": symbol,
                        "direction": direction,
                        "entry_price": actual_entry_price,
                        "lot": current_lot_size
                    }
                else:
                    print(f"Order send failed for {symbol} ({direction})! Retcode: {result.retcode}, Message: {getattr(result, 'comment', '')}")
        # Exit logic for all open positions
        symbols_to_remove = []
        for symbol, active_position in active_positions.items():
            df = get_latest_data(symbol)
            curr_bar = df.iloc[-1]
            lower_close_price = curr_bar['close']
            tick = mt5.symbol_info_tick(symbol)
            exit_price = tick.bid if active_position['direction'] == 'long' else tick.ask
            if active_position['direction'] == 'long':
                if lower_close_price - active_position['entry_price'] >= take_profit:
                    close_position(active_position['ticket'])
                    lot_size_index = min(lot_size_index + 1, len(lot_size_sequence) - 1)
                    current_lot_size = lot_size_sequence[lot_size_index]
                    account_info = mt5.account_info()
                    capital = account_info.balance if account_info else capital
                    position_info = mt5.history_deals_get(ticket=active_position['ticket'])
                    pnl = position_info[0].profit if position_info else 0.0
                    performance['net_pnl'] += pnl
                    if pnl > 0:
                        performance['profit_count'] += 1
                    else:
                        performance['loss_count'] += 1
                    history.append([
                        str(datetime.now()), symbol, 'long', f"{active_position['entry_price']:.2f}", f"{exit_price:.2f}", f"{pnl:.2f}"
                    ])
                    symbols_to_remove.append(symbol)
                elif lower_close_price - active_position['entry_price'] <= -stop_loss:
                    close_position(active_position['ticket'])
                    lot_size_index = 0
                    current_lot_size = lot_size_sequence[lot_size_index]
                    account_info = mt5.account_info()
                    capital = account_info.balance if account_info else capital
                    position_info = mt5.history_deals_get(ticket=active_position['ticket'])
                    pnl = position_info[0].profit if position_info else 0.0
                    performance['net_pnl'] += pnl
                    if pnl > 0:
                        performance['profit_count'] += 1
                    else:
                        performance['loss_count'] += 1
                    history.append([
                        str(datetime.now()), symbol, 'long', f"{active_position['entry_price']:.2f}", f"{exit_price:.2f}", f"{pnl:.2f}"
                    ])
                    symbols_to_remove.append(symbol)
            else:
                if active_position['entry_price'] - lower_close_price >= take_profit:
                    close_position(active_position['ticket'])
                    lot_size_index = min(lot_size_index + 1, len(lot_size_sequence) - 1)
                    current_lot_size = lot_size_sequence[lot_size_index]
                    account_info = mt5.account_info()
                    capital = account_info.balance if account_info else capital
                    position_info = mt5.history_deals_get(ticket=active_position['ticket'])
                    pnl = position_info[0].profit if position_info else 0.0
                    performance['net_pnl'] += pnl
                    if pnl > 0:
                        performance['profit_count'] += 1
                    else:
                        performance['loss_count'] += 1
                    history.append([
                        str(datetime.now()), symbol, 'short', f"{active_position['entry_price']:.2f}", f"{exit_price:.2f}", f"{pnl:.2f}"
                    ])
                    symbols_to_remove.append(symbol)
                elif active_position['entry_price'] - lower_close_price <= -stop_loss:
                    close_position(active_position['ticket'])
                    lot_size_index = 0
                    current_lot_size = lot_size_sequence[lot_size_index]
                    account_info = mt5.account_info()
                    capital = account_info.balance if account_info else capital
                    position_info = mt5.history_deals_get(ticket=active_position['ticket'])
                    pnl = position_info[0].profit if position_info else 0.0
                    performance['net_pnl'] += pnl
                    if pnl > 0:
                        performance['profit_count'] += 1
                    else:
                        performance['loss_count'] += 1
                    history.append([
                        str(datetime.now()), symbol, 'short', f"{active_position['entry_price']:.2f}", f"{exit_price:.2f}", f"{pnl:.2f}"
                    ])
                    symbols_to_remove.append(symbol)
        # Remove closed positions
        for symbol in symbols_to_remove:
            active_positions.pop(symbol, None)
        # Print dashboard in terminal
        print("\n================ DASHBOARD ================")
        print(f"Capital: {capital:.2f}")
        print(f"Net PnL: {performance['net_pnl']:.2f}")
        print(f"Profitable Trades: {performance['profit_count']}")
        print(f"Loss Trades: {performance['loss_count']}")
        print("\nOpen Positions:")
        if active_positions:
            for symbol, pos in active_positions.items():
                print(f"  {symbol}: {pos['direction']} | Entry: {pos['entry_price']:.2f} | Lot: {pos['lot']} | Ticket: {pos['ticket']}")
        else:
            print("  None")
        print("\nRecent Trades:")
        for h in history[-8:]:
            print(f"  Time: {h[0]} | Symbol: {h[1]} | Dir: {h[2]} | Entry: {h[3]} | Exit: {h[4]} | PnL: {h[5]}")
        print("\nIndicators:")
        for symbol in symbols:
            try:
                df = get_latest_data(symbol)
                curr_bar = df.iloc[-1]
                prev_bar = df.iloc[-2] if len(df) > 1 else curr_bar
                rsi_lower = f"{curr_bar['RSI_lower']:.2f}"
                rsi_higher = f"{curr_bar['RSI_higher']:.2f}"
                ema15_lower = f"{curr_bar['EMA15_lower']:.2f}"
                close = f"{curr_bar['close']:.2f}"
                prev_rsi_lower = prev_bar['RSI_lower']
                time_str = str(curr_bar['time']) if 'time' in curr_bar and not pd.isna(curr_bar['time']) else "-"
                rsi_cross_long = prev_rsi_lower < 30 and curr_bar['RSI_lower'] >= 30
                m5_rsi_long = curr_bar['RSI_higher'] > 50
                ema_long = curr_bar['close'] < curr_bar['EMA15_lower']
                rsi_cross_short = prev_rsi_lower > 70 and curr_bar['RSI_lower'] <= 70
                m5_rsi_short = curr_bar['RSI_higher'] < 50
                ema_short = curr_bar['close'] > curr_bar['EMA15_lower']
                entry_long = rsi_cross_long and m5_rsi_long and ema_long
                entry_short = rsi_cross_short and m5_rsi_short and ema_short
                entry_signal = "Long" if entry_long else ("Short" if entry_short else "-")
                print(f"  {symbol} | Time: {time_str} | RSI_lower: {rsi_lower} | RSI_higher: {rsi_higher} | EMA15_lower: {ema15_lower} | Close: {close} | RSI Cross: {rsi_cross_long or rsi_cross_short} | M5 RSI Filter: {m5_rsi_long or m5_rsi_short} | EMA Filter: {ema_long or ema_short} | Entry Signal: {entry_signal}")
            except Exception:
                print(f"  {symbol} | - | - | - | - | - | - | - | - | -")
        print("===========================================\n")
    mt5.shutdown()

if __name__ == "__main__":
    main()
