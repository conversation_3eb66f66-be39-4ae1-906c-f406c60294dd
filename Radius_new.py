from tabulate import tabulate
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# === USER SETTINGS ===
symbol = "Volatility 100 (1s) Index"
timeframe = mt5.TIMEFRAME_M1
bars = 5000

step = 0.12
multi = 2.0
atr_period = 14
rsi_period = 14
rsi_thresh = 50
confirm_bars = 3
n = 3

# Backtesting parameters
lot_size = 0.7
spread = 0.28  # Realistic Volatility 100 (1s) Index spread (both entry and exit)

# === CONNECT TO MT5 ===
if not mt5.initialize():
    raise RuntimeError("MT5 initialization failed")

rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, bars)
mt5.shutdown()

# === CONVERT TO PANDAS DF ===
df = pd.DataFrame(rates)
df['time'] = pd.to_datetime(df['time'], unit='s')
df.set_index('time', inplace=True)

# === BASIC CALCULATIONS ===
df['hl2'] = (df['high'] + df['low']) / 2
df['atr'] = df['high'].rolling(atr_period).max() - df['low'].rolling(atr_period).min()
df['atr'] = df['atr'].ewm(span=atr_period).mean()
df['rsi'] = df['close'].diff().apply(lambda x: max(x,0)).rolling(rsi_period).mean() / \
            df['close'].diff().abs().rolling(rsi_period).mean() * 100

# === TREND LOGIC INITIALIZATION ===
band = [np.nan] * len(df)
trend = [None] * len(df)
m1, m2 = 0.0, 0.0
band_val = 0.0

for i in range(len(df)):
    if i < 101:
        trend[i] = None
        continue

    distance = df['atr'].iloc[i] * multi
    distance1 = df['atr'].iloc[i] * 0.5
    rsi_ok = df['rsi'].iloc[i] > rsi_thresh

    # Initialize
    if i == 101:
        trend[i] = True
        band_val = df['low'].iloc[i] * 0.9
        band[i] = band_val
        continue

    up_confirm = all(df['close'].iloc[i - confirm_bars:i] > band_val)
    down_confirm = all(df['close'].iloc[i - confirm_bars:i] < band_val)

    if down_confirm and rsi_ok:
        trend[i] = False
        band_val = df['high'].iloc[i] + distance
    elif up_confirm and rsi_ok:
        trend[i] = True
        band_val = df['low'].iloc[i] - distance
    else:
        trend[i] = trend[i-1]

    if i % n == 0:
        if trend[i]:
            m1 = 0.0
            m2 += step
            band_val += distance1 * m2
        else:
            m1 += step
            m2 = 0.0
            band_val -= distance1 * m1

    band[i] = band_val

# === SMOOTHING ===
df['band'] = pd.Series(band).ewm(span=n).mean().ewm(span=3).mean()
df['trend'] = trend

# Convert None values to False for proper boolean operations
df['trend'] = df['trend'].fillna(False)
df['trend'] = df['trend'].astype(bool)

df['band_upper'] = df['band'] + df['atr'] * multi * 0.6
df['band_lower'] = df['band'] - df['atr'] * multi * 0.6
df['band_final'] = np.where(df['trend'], df['band_upper'], df['band_lower'])

# === SIGNALS ===
df['trend_change'] = df['trend'] != df['trend'].shift(1)
df['long_entry'] = df['trend_change'] & df['trend']
df['short_entry'] = df['trend_change'] & (~df['trend'])

# === BACKTESTING LOGIC ===
# LOOK-AHEAD BIAS FIXES APPLIED:
# 1. Support/Resistance: Removed center=True, added shift(1) to avoid future data
# 2. Exit Logic: Use high/low of current bar to check if TP/SL were hit (realistic)
# 3. Entry Logic: Uses only current and past data
# Advanced High-Performance Strategy
positions = []
active_trade = None
equity_curve = [0]

# Calculate comprehensive technical indicators
df['ema20'] = df['close'].ewm(span=20).mean()
df['ema50'] = df['close'].ewm(span=50).mean()
df['sma9'] = df['close'].rolling(window=9).mean()
df['sma200'] = df['close'].rolling(window=200).mean()

# Enhanced RSI with multiple timeframes
def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

df['rsi'] = calculate_rsi(df['close'], 14)
df['rsi_fast'] = calculate_rsi(df['close'], 7)
df['rsi_slow'] = calculate_rsi(df['close'], 21)

# Enhanced volume analysis
df['volume_sma'] = df['tick_volume'].rolling(window=20).mean()
df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
df['volume_surge'] = df['volume_ratio'] > 1.5

# Price action patterns
df['price_momentum'] = df['close'] / df['close'].shift(3) - 1
df['price_velocity'] = df['close'].diff(3)
df['candle_body'] = abs(df['close'] - df['open'])
df['candle_range'] = df['high'] - df['low']
df['body_ratio'] = df['candle_body'] / df['candle_range']

# Advanced ATR and volatility
def true_range(high, low, close):
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

df['true_atr'] = true_range(df['high'], df['low'], df['close']).rolling(window=14).mean()
df['atr_fast'] = true_range(df['high'], df['low'], df['close']).rolling(window=7).mean()

# Market structure analysis
df['higher_high'] = (df['high'] > df['high'].shift(1)) & (df['high'].shift(1) > df['high'].shift(2))
df['lower_low'] = (df['low'] < df['low'].shift(1)) & (df['low'].shift(1) < df['low'].shift(2))
df['bullish_structure'] = df['higher_high'].rolling(5).sum() > df['lower_low'].rolling(5).sum()

# Support/Resistance levels - FIXED: No look-ahead bias
def find_support_resistance(closes, window=20):
    # Use only past data - no center=True
    highs = closes.rolling(window, center=False).max().shift(1)  # Shift to avoid current bar
    lows = closes.rolling(window, center=False).min().shift(1)   # Shift to avoid current bar
    return highs, lows

df['resistance'], df['support'] = find_support_resistance(df['close'])

# Trend confluence
df['trend_ema'] = df['ema20'] > df['ema50']
df['trend_price'] = df['close'] > df['sma200']
df['trend_structure'] = df['bullish_structure']
df['trend_score'] = df['trend_ema'].astype(int) + df['trend_price'].astype(int) + df['trend_structure'].astype(int)

# Optimized entry strategy balancing quality and quantity
def get_high_quality_entry_conditions(i):
    """Optimized entry logic that balances high quality with reasonable trade frequency"""
    
    # Current market data
    trend = df['trend'].iloc[i]
    trend_change = df['trend_change'].iloc[i]
    atr = df['true_atr'].iloc[i]
    rsi = df['rsi'].iloc[i]
    price = df['close'].iloc[i]
    ema20 = df['ema20'].iloc[i]
    ema50 = df['ema50'].iloc[i]
    volume_ratio = df['volume_ratio'].iloc[i]
    price_momentum = df['price_momentum'].iloc[i]
    trend_score = df['trend_score'].iloc[i]
    
    # Skip if insufficient data
    if pd.isna(atr) or atr <= 0 or pd.isna(rsi) or pd.isna(ema20):
        return False, False, None
    
    # Balanced ATR requirement for better trade frequency
    min_atr_required = spread * 2.5  # Need 2.5x spread minimum for quality
    if atr < min_atr_required:
        return False, False, None
    
    # Flexible trend confirmation approach
    trend_age = 0
    for j in range(max(0, i-3), i):  # Reduced from 5 to 3 bars
        if df['trend'].iloc[j] == trend:
            trend_age += 1
        else:
            break
    
    # More flexible trend requirements
    trend_ok = trend_age >= 1 or trend_change  # Either 1 bar confirmation OR fresh change
    
    if not trend_ok:
        return False, False, None
    
    # Optimized risk/reward parameters
    reward_ratio = 2.5  # Balanced reward for spread management
    atr_multiplier = 1.2  # Balanced stops
    
    # HIGH-QUALITY LONG conditions - selective but achievable
    long_conditions = [
        trend == True,  # Primary trend up
        40 < rsi < 75,  # Wider RSI range for more opportunities
        price > ema20,  # Price above short-term MA
        ema20 > ema50 * 0.9999,  # Trend alignment (more flexible)
        volume_ratio > 1.0,  # Volume confirmation
        price_momentum > -0.002,  # Not strongly negative (more flexible)
        trend_score >= 1,  # Reasonable trend confluence
    ]
    
    # HIGH-QUALITY SHORT conditions - selective but achievable
    short_conditions = [
        trend == False,  # Primary trend down
        25 < rsi < 60,  # Wider RSI range for more opportunities
        price < ema20,  # Price below short-term MA
        ema20 < ema50 * 1.0001,  # Trend alignment (more flexible)
        volume_ratio > 1.0,  # Volume confirmation
        price_momentum < 0.002,  # Not strongly positive (more flexible)
        trend_score <= 2,  # Reasonable trend confluence
    ]
    
    # Support/Resistance filters (if available)
    if not pd.isna(df['support'].iloc[i]):
        long_conditions.append(price > df['support'].iloc[i] * 0.999)  # Near or above support
    else:
        long_conditions.append(True)  # Always true if no support data
        
    if not pd.isna(df['resistance'].iloc[i]):
        short_conditions.append(price < df['resistance'].iloc[i] * 1.001)  # Near or below resistance
    else:
        short_conditions.append(True)  # Always true if no resistance data
    
    # Need 6 out of 8 conditions (75% quality score) - balanced approach
    long_entry = sum(long_conditions) >= 6
    short_entry = sum(short_conditions) >= 6
    
    params = {
        'reward_ratio': reward_ratio,
        'atr_multiplier': atr_multiplier,
        'conditions_met': max(sum(long_conditions), sum(short_conditions)),
        'trend_age': trend_age,
        'total_conditions': 8
    }
    
    return long_entry, short_entry, params

# Performance tracking for adaptive management
performance_tracker = {
    'recent_trades': [],
    'win_rate': 0.5,
    'consecutive_losses': 0,
    'max_consecutive_losses': 0,
    'total_trades': 0
}

def update_performance_tracker(pnl):
    """Track performance and adapt strategy"""
    performance_tracker['recent_trades'].append(1 if pnl > 0 else 0)
    if len(performance_tracker['recent_trades']) > 20:
        performance_tracker['recent_trades'].pop(0)
    
    performance_tracker['win_rate'] = sum(performance_tracker['recent_trades']) / len(performance_tracker['recent_trades']) if performance_tracker['recent_trades'] else 0.5
    performance_tracker['total_trades'] += 1
    
    if pnl <= 0:
        performance_tracker['consecutive_losses'] += 1
        performance_tracker['max_consecutive_losses'] = max(performance_tracker['max_consecutive_losses'], performance_tracker['consecutive_losses'])
    else:
        performance_tracker['consecutive_losses'] = 0

# Main trading loop with enhanced logic
signal_debug = []
for i in range(max(200, atr_period, rsi_period) + 10, len(df)):
    current_time = df.index[i]
    current_price = df['close'].iloc[i]
    current_atr = df['true_atr'].iloc[i]
    
    # Skip if insufficient data
    if pd.isna(current_atr) or current_atr <= 0:
        continue
    
    if active_trade is None:
        # Get enhanced entry signals
        long_signal, short_signal, params = get_high_quality_entry_conditions(i)
        
        # Debug information
        if i % 100 == 0:  # Log every 100 bars
            signal_debug.append({
                'i': i,
                'trend': df['trend'].iloc[i],
                'trend_change': df['trend_change'].iloc[i],
                'rsi': df['rsi'].iloc[i],
                'long_signal': long_signal,
                'short_signal': short_signal
            })
        
        # Balanced risk management for optimal trade frequency
        if performance_tracker['consecutive_losses'] >= 3:  # Allow 3 consecutive losses
            continue
            
        # Standard position sizing for better returns
        current_lot_size = lot_size  # Full size for better profits
        
        if long_signal and params:
            # Ultra-selective entry with enhanced spread cost management
            entry_price = current_price + spread
            
            # Wider stop loss for better survival rate
            sl_distance = current_atr * params['atr_multiplier']  # 1.5x ATR
            sl = entry_price - sl_distance
            
            # Higher take profit for better spread coverage
            tp_distance = sl_distance * params['reward_ratio']  # 3.0:1 ratio
            tp = entry_price + tp_distance - spread
            
            # Balanced spread cost analysis
            total_spread_cost = spread * 2 * current_lot_size
            potential_profit = tp_distance * current_lot_size
            
            # Only take trades where potential profit is at least 3x the spread cost
            if potential_profit < total_spread_cost * 3:
                continue
                
            # Ensure the trade makes sense
            if tp <= entry_price or sl >= entry_price:
                continue
                
            active_trade = {
                'Type': 'Long',
                'Entry Time': current_time,
                'Entry Price': entry_price,
                'Size': current_lot_size,
                'SL': sl,
                'TP': tp,
                'Exit Time': None,
                'Exit Price': None,
                'PnL': None,
                'Reward_Ratio': params['reward_ratio'],
                'Quality_Score': params['conditions_met'],
                'Entry_RSI': df['rsi'].iloc[i],
                'Risk_Amount': sl_distance * current_lot_size,
                'Trend_Age': params['trend_age']
            }
            
        elif short_signal and params:
            # Ultra-selective entry with enhanced spread cost management
            entry_price = current_price - spread
            
            # Wider stop loss for better survival rate
            sl_distance = current_atr * params['atr_multiplier']  # 1.5x ATR
            sl = entry_price + sl_distance
            
            # Higher take profit for better spread coverage
            tp_distance = sl_distance * params['reward_ratio']  # 3.0:1 ratio
            tp = entry_price - tp_distance + spread
            
            # Balanced spread cost analysis
            total_spread_cost = spread * 2 * current_lot_size  # Entry + exit spread
            potential_profit = tp_distance * current_lot_size
            
            # Only take trades where potential profit is at least 3x the spread cost
            if potential_profit < total_spread_cost * 3:
                continue
                
            # Ensure the trade makes sense
            if tp >= entry_price or sl <= entry_price:
                continue
                
            active_trade = {
                'Type': 'Short',
                'Entry Time': current_time,
                'Entry Price': entry_price,
                'Size': current_lot_size,
                'SL': sl,
                'TP': tp,
                'Exit Time': None,
                'Exit Price': None,
                'PnL': None,
                'Reward_Ratio': params['reward_ratio'],
                'Quality_Score': params['conditions_met'],
                'Entry_RSI': df['rsi'].iloc[i],
                'Risk_Amount': sl_distance * current_lot_size,
                'Trend_Age': params['trend_age']
            }
    
    else:
        # Much simpler exit management - just SL and TP
        if active_trade['Type'] == 'Long':
            # Only check SL and TP
            sl_hit = df['low'].iloc[i] <= active_trade['SL']
            tp_hit = df['high'].iloc[i] >= active_trade['TP']
            
            if sl_hit:
                exit_price = active_trade['SL'] - spread
                exit_reason = 'Stop_Loss'
                pnl = (exit_price - active_trade['Entry Price']) * active_trade['Size']
                update_performance_tracker(pnl)
                
                active_trade.update({
                    'Exit Time': current_time,
                    'Exit Price': exit_price,
                    'PnL': pnl,
                    'Exit_Reason': exit_reason
                })
                positions.append(active_trade.copy())
                equity_curve.append(equity_curve[-1] + pnl)
                active_trade = None
                
            elif tp_hit:
                exit_price = active_trade['TP'] - spread
                exit_reason = 'Take_Profit'
                pnl = (exit_price - active_trade['Entry Price']) * active_trade['Size']
                update_performance_tracker(pnl)
                
                active_trade.update({
                    'Exit Time': current_time,
                    'Exit Price': exit_price,
                    'PnL': pnl,
                    'Exit_Reason': exit_reason
                })
                positions.append(active_trade.copy())
                equity_curve.append(equity_curve[-1] + pnl)
                active_trade = None
        
        elif active_trade['Type'] == 'Short':
            # Only check SL and TP
            sl_hit = df['high'].iloc[i] >= active_trade['SL']
            tp_hit = df['low'].iloc[i] <= active_trade['TP']
            
            if sl_hit:
                exit_price = active_trade['SL'] + spread
                exit_reason = 'Stop_Loss'
                pnl = (active_trade['Entry Price'] - exit_price) * active_trade['Size']
                update_performance_tracker(pnl)
                
                active_trade.update({
                    'Exit Time': current_time,
                    'Exit Price': exit_price,
                    'PnL': pnl,
                    'Exit_Reason': exit_reason
                })
                positions.append(active_trade.copy())
                equity_curve.append(equity_curve[-1] + pnl)
                active_trade = None
                
            elif tp_hit:
                exit_price = active_trade['TP'] + spread
                exit_reason = 'Take_Profit'
                pnl = (active_trade['Entry Price'] - exit_price) * active_trade['Size']
                update_performance_tracker(pnl)
                
                active_trade.update({
                    'Exit Time': current_time,
                    'Exit Price': exit_price,
                    'PnL': pnl,
                    'Exit_Reason': exit_reason
                })
                positions.append(active_trade.copy())
                equity_curve.append(equity_curve[-1] + pnl)
                active_trade = None

# Close any remaining position at the end
if active_trade is not None:
    final_time = df.index[-1]
    final_close = df['close'].iloc[-1]
    
    if active_trade['Type'] == 'Long':
        exit_price = final_close - spread  # Pay spread on exit
        pnl = (exit_price - active_trade['Entry Price']) * active_trade['Size']
    else:  # Short
        exit_price = final_close + spread  # Pay spread on exit
        pnl = (active_trade['Entry Price'] - exit_price) * active_trade['Size']
    
    active_trade.update({
        'Exit Time': final_time,
        'Exit Price': exit_price,
        'PnL': pnl,
        'Exit_Reason': 'End_of_Data'
    })
    positions.append(active_trade.copy())
    equity_curve.append(equity_curve[-1] + pnl)

# Debug output
print(f"\n🔍 Signal Debug Information:")
print(f"Total bars processed: {len(df) - max(200, atr_period, rsi_period) - 10}")
print(f"Trend changes found: {df['trend_change'].sum()}")
print(f"Sample signal checks:")
for debug_info in signal_debug[:5]:  # Show first 5 debug entries
    print(f"  Bar {debug_info['i']}: Trend={debug_info['trend']}, Change={debug_info['trend_change']}, RSI={debug_info['rsi']:.1f}, Long={debug_info['long_signal']}, Short={debug_info['short_signal']}")

# Calculate performance metrics
net_pnl = sum([p['PnL'] for p in positions if p['PnL'] is not None])
equity_curve = np.array(equity_curve)
if len(equity_curve) > 1:
    roll_max = np.maximum.accumulate(equity_curve)
    drawdown = roll_max - equity_curve
    max_drawdown = np.max(drawdown)
else:
    max_drawdown = 0

# Format time columns for readability
def fmt_time(t):
    if pd.isnull(t):
        return ""
    if isinstance(t, (np.datetime64, pd.Timestamp)):
        return pd.to_datetime(t).strftime("%Y-%m-%d %H:%M:%S")
    try:
        return pd.to_datetime(t).strftime("%Y-%m-%d %H:%M:%S")
    except:
        return str(t)

# Create positions table
pos_table = []
for p in positions:
    pos_table.append([
        p['Type'],
        fmt_time(p['Entry Time']),
        f"{p['Entry Price']:.5f}",
        f"{p['SL']:.5f}",
        f"{p['TP']:.5f}",
        fmt_time(p['Exit Time']),
        f"{p['Exit Price']:.5f}" if p['Exit Price'] is not None else "",
        p.get('Exit_Reason', 'Unknown'),
        f"{p.get('Quality_Score', 0)}/8",
        f"{p.get('Reward_Ratio', 2.0):.1f}:1",
        p['Size'],
        f"{p['PnL']:.2f}" if p['PnL'] is not None else ""
    ])

pos_headers = ["Type", "Entry Time", "Entry Price", "SL", "TP", "Exit Time", "Exit Price", "Exit Reason", "Quality", "R:R", "Size", "PnL"]
print("🔮 High-Performance Advanced Radius Trend Strategy - Position Results")
print("=" * 140)
print(tabulate(pos_table, headers=pos_headers, tablefmt="fancy_grid"))

# Enhanced performance statistics
print(f"\n📊 Enhanced Adaptive Performance Summary (Spread: {spread} per trade):")
print(f"Total Positions: {len(positions)}")
print(f"Net PnL: {net_pnl:.2f}")
print(f"Max Drawdown: {max_drawdown:.2f}")
if len(positions) > 0:
    total_spread_cost = len(positions) * spread * 2 * lot_size  # Both entry and exit
    print(f"Total Spread Cost: {total_spread_cost:.2f}")
    print(f"Net PnL after Spread: {net_pnl:.2f} (already included)")
    print(f"Spread Cost per Trade: {spread * 2 * lot_size:.2f}")

winning_trades = [p for p in positions if p['PnL'] is not None and p['PnL'] > 0]
losing_trades = [p for p in positions if p['PnL'] is not None and p['PnL'] < 0]

if len(positions) > 0:
    win_rate = len(winning_trades) / len([p for p in positions if p['PnL'] is not None]) * 100
    print(f"Win Rate: {win_rate:.1f}%")
    
    if winning_trades:
        avg_win = np.mean([p['PnL'] for p in winning_trades])
        max_win = np.max([p['PnL'] for p in winning_trades])
        print(f"Average Win: {avg_win:.2f}")
        print(f"Largest Win: {max_win:.2f}")
    
    if losing_trades:
        avg_loss = np.mean([p['PnL'] for p in losing_trades])
        max_loss = np.min([p['PnL'] for p in losing_trades])
        print(f"Average Loss: {avg_loss:.2f}")
        print(f"Largest Loss: {max_loss:.2f}")
        
    if winning_trades and losing_trades:
        profit_factor = abs(sum([p['PnL'] for p in winning_trades])) / abs(sum([p['PnL'] for p in losing_trades]))
        print(f"Profit Factor: {profit_factor:.2f}")
        
        # Risk-adjusted metrics
        avg_trade = net_pnl / len(positions)
        print(f"Average Trade: {avg_trade:.2f}")
        
        # Exit reason analysis
        tp_count = len([p for p in positions if p.get('Exit_Reason') == 'Take_Profit'])
        sl_count = len([p for p in positions if p.get('Exit_Reason') == 'Stop_Loss'])
        early_count = len([p for p in positions if p.get('Exit_Reason') == 'Early_Exit'])
        
        print(f"\n📈 Exit Analysis:")
        print(f"Take Profit Exits: {tp_count} ({tp_count/len(positions)*100:.1f}%)")
        print(f"Stop Loss Exits: {sl_count} ({sl_count/len(positions)*100:.1f}%)")
        print(f"Early Exits: {early_count} ({early_count/len(positions)*100:.1f}%)")
        
        # Market regime analysis
        regime_stats = {}
        for p in positions:
            regime = p.get('Market_Regime', 'Unknown')
            if regime not in regime_stats:
                regime_stats[regime] = {'count': 0, 'pnl': 0}
            regime_stats[regime]['count'] += 1
            regime_stats[regime]['pnl'] += p['PnL']
        
        print(f"\n🌊 Market Regime Performance:")
        for regime, stats in regime_stats.items():
            avg_pnl = stats['pnl'] / stats['count']
            print(f"{regime}: {stats['count']} trades, Avg PnL: {avg_pnl:.2f}")
        
        # Dynamic reward ratio analysis
        reward_ratios = [p.get('Reward_Ratio', 2.5) for p in positions]
        avg_reward_ratio = np.mean(reward_ratios)
        print(f"\nAverage Reward Ratio Used: {avg_reward_ratio:.2f}:1")
        print(f"Reward Ratio Range: {min(reward_ratios):.1f} - {max(reward_ratios):.1f}")
        
    else:
        print("Dynamic Adaptive Strategy - No reward ratio (no trades)")
    
    # Sharpe-like ratio (simplified)
    if len(equity_curve) > 1:
        returns = np.diff(equity_curve)
        if np.std(returns) > 0:
            sharpe_like = np.mean(returns) / np.std(returns) * np.sqrt(len(returns))
            print(f"Risk-Adjusted Return Metric: {sharpe_like:.2f}")
            
        # Maximum consecutive wins/losses
        consecutive_wins = 0
        consecutive_losses = 0
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        
        for p in positions:
            if p['PnL'] > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
            elif p['PnL'] < 0:
                consecutive_losses += 1
                consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
                
        print(f"Max Consecutive Wins: {max_consecutive_wins}")
        print(f"Max Consecutive Losses: {max_consecutive_losses}")
        
        # Adaptive learning effectiveness
        print(f"\n🧠 Advanced Strategy Performance:")
        print(f"Final Win Rate Tracking: {performance_tracker['win_rate']:.1%}")
        print(f"Max Consecutive Losses: {performance_tracker['max_consecutive_losses']}")
        print(f"Total Trades Executed: {performance_tracker['total_trades']}")
        print(f"Strategy used advanced multi-factor analysis with {len(positions)} high-quality setups")

# === PLOT ===
plt.figure(figsize=(16, 10))

# Main price chart
plt.subplot(2, 1, 1)
plt.plot(df['close'], label='Close Price', alpha=0.7, linewidth=1)
plt.plot(df['band'], label='Smoothed Band', color='gray', linewidth=1.5)
plt.plot(df['band_final'], label='Radius Band', linewidth=2, linestyle='--')

# Fill trend areas
plt.fill_between(df.index, df['band'], df['hl2'].rolling(20).mean(),
                 where=df['trend'], color='green', alpha=0.2, label='Up Trend')
plt.fill_between(df.index, df['band'], df['hl2'].rolling(20).mean(),
                 where=~df['trend'], color='red', alpha=0.2, label='Down Trend')

# Plot entry signals
long_entries = df[df['long_entry']]
short_entries = df[df['short_entry']]

if not long_entries.empty:
    plt.plot(long_entries.index, df['close'][long_entries.index], '^', 
             markersize=10, color='green', label='Buy Signal')
if not short_entries.empty:
    plt.plot(short_entries.index, df['close'][short_entries.index], 'v', 
             markersize=10, color='red', label='Sell Signal')

plt.title("🔮 Advanced Radius Trend Strategy")
plt.legend()
plt.grid(True, alpha=0.3)

# Equity curve
plt.subplot(2, 1, 2)
plt.plot(equity_curve, color='blue', linewidth=2)
plt.title("Equity Curve")
plt.xlabel("Trade Number")
plt.ylabel("Cumulative PnL")
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
