from tabulate import tabulate
import MetaTrader5 as mt5
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# --- Parameters ---
SYMBOL = "Volatility 100 (1s) Index"  # Change to your symbol
TIMEFRAME = mt5.TIMEFRAME_M5
DAYS = 30

# --- Connect to MT5 ---
if not mt5.initialize():
    print("initialize() failed")
    quit()

# --- Fetch 3 days of 1-min data ---
end = datetime.now()
start = end - timedelta(days=DAYS)
rates = mt5.copy_rates_range(SYMBOL, TIMEFRAME, start, end)
mt5.shutdown()

if rates is None or len(rates) == 0:
    print("No data fetched.")
    quit()

# Convert to DataFrame
candles = pd.DataFrame(rates)
candles['time'] = pd.to_datetime(candles['time'], unit='s')


# --- Double EMA 200 Function ---

# --- Correct Double EMA (DEMA) Function ---
def dema(series, period):
    ema1 = series.ewm(span=period, adjust=False).mean()
    ema2 = ema1.ewm(span=period, adjust=False).mean()
    return 2 * ema1 - ema2

# --- Calculate DEMA200 ---
close_prices = candles['close']
dema200 = dema(close_prices, 25)

# --- Standard Supertrend Function ---

# --- Correct Supertrend Calculation ---
def supertrend(df, period=12, multiplier=3):
    high = df['high'].values
    low = df['low'].values
    close = df['close'].values
    n = len(df)
    tr = np.zeros(n)
    tr[0] = high[0] - low[0]
    for i in range(1, n):
        tr[i] = max(high[i] - low[i], abs(high[i] - close[i-1]), abs(low[i] - close[i-1]))
    atr = pd.Series(tr).rolling(window=period, min_periods=1).mean().values
    basic_upper = (high + low) / 2 + multiplier * atr
    basic_lower = (high + low) / 2 - multiplier * atr
    final_upper = np.copy(basic_upper)
    final_lower = np.copy(basic_lower)
    supertrend = np.full(n, np.nan)
    direction = np.ones(n, dtype=bool)  # True=up, False=down
    for i in range(1, n):
        if close[i-1] > final_upper[i-1]:
            final_upper[i] = basic_upper[i]
        else:
            final_upper[i] = min(basic_upper[i], final_upper[i-1])
        if close[i-1] < final_lower[i-1]:
            final_lower[i] = basic_lower[i]
        else:
            final_lower[i] = max(basic_lower[i], final_lower[i-1])
        # Determine direction and supertrend value
        if supertrend[i-1] == final_upper[i-1] or (np.isnan(supertrend[i-1]) and close[i] <= final_upper[i]):
            if close[i] <= final_upper[i]:
                supertrend[i] = final_upper[i]
                direction[i] = False
            else:
                supertrend[i] = final_lower[i]
                direction[i] = True
        elif supertrend[i-1] == final_lower[i-1] or (np.isnan(supertrend[i-1]) and close[i] > final_upper[i]):
            if close[i] >= final_lower[i]:
                supertrend[i] = final_lower[i]
                direction[i] = True
            else:
                supertrend[i] = final_upper[i]
                direction[i] = False
    return supertrend


# --- Calculate Supertrend ---
st = supertrend(candles)


# --- Backtesting Logic ---
positions = []  # Each position: dict with entry/exit info
lot_size = 0.7
spread = 0.28
in_position = False
position_type = None  # 'long' or 'short'
entry_idx = None
entry_price = None
exit_idx = None
exit_price = None
max_drawdown = 0
equity_curve = [0]



for i in range(200, len(candles)-1):  # start after enough data for DEMA200 and supertrend
    if not in_position:
        # Long entry: close above DEMA200 and above supertrend
        if candles['close'].values[i] > dema200.values[i] and candles['close'].values[i] > st[i]:
            entry_idx = i+1
            entry_price = candles['open'].values[entry_idx] + spread
            sl_raw = st[entry_idx]  # Initial stop loss at supertrend value
            # Limit SL to maximum 4.1 points from entry price
            max_sl_distance = 4.1
            sl = max(sl_raw, entry_price - max_sl_distance)
            positions.append({
                'Type': 'Long',
                'Entry Time': candles['time'].values[entry_idx],
                'Entry Price': entry_price,
                'Size': lot_size,
                'SL': sl,
                'Exit Time': None,
                'Exit Price': None,
                'PnL': None
            })
            in_position = True
            position_type = 'long'
        # Short entry: close below DEMA200 and below supertrend
        elif candles['close'].values[i] < dema200.values[i] and candles['close'].values[i] < st[i]:
            entry_idx = i+1
            entry_price = candles['open'].values[entry_idx] - spread
            sl_raw = st[entry_idx]  # Initial stop loss at supertrend value
            # Limit SL to maximum 4.1 points from entry price
            max_sl_distance = 4.1
            sl = min(sl_raw, entry_price + max_sl_distance)
            positions.append({
                'Type': 'Short',
                'Entry Time': candles['time'].values[entry_idx],
                'Entry Price': entry_price,
                'Size': lot_size,
                'SL': sl,
                'Exit Time': None,
                'Exit Price': None,
                'PnL': None
            })
            in_position = True
            position_type = 'short'
    else:
        # Manage open position with SL at supertrend
        if position_type == 'long':
            # Exit if close below supertrend (normal exit)
            if candles['close'].values[i] < st[i]:
                exit_idx = i+1
                exit_price = candles['open'].values[exit_idx] - spread
                positions[-1]['Exit Time'] = candles['time'].values[exit_idx]
                positions[-1]['Exit Price'] = exit_price
                positions[-1]['PnL'] = (exit_price - positions[-1]['Entry Price']) * lot_size
                in_position = False
                position_type = None
                equity_curve.append(equity_curve[-1] + positions[-1]['PnL'])
            # Stop loss: if price reverses to supertrend (close <= SL)
            elif candles['close'].values[i] <= positions[-1]['SL']:
                exit_idx = i+1
                exit_price = positions[-1]['SL'] - spread
                positions[-1]['Exit Time'] = candles['time'].values[exit_idx]
                positions[-1]['Exit Price'] = exit_price
                positions[-1]['PnL'] = (exit_price - positions[-1]['Entry Price']) * lot_size
                in_position = False
                position_type = None
                equity_curve.append(equity_curve[-1] + positions[-1]['PnL'])
        elif position_type == 'short':
            # Exit if close above supertrend (normal exit)
            if candles['close'].values[i] > st[i]:
                exit_idx = i+1
                exit_price = candles['open'].values[exit_idx] + spread
                positions[-1]['Exit Time'] = candles['time'].values[exit_idx]
                positions[-1]['Exit Price'] = exit_price
                positions[-1]['PnL'] = (positions[-1]['Entry Price'] - exit_price) * lot_size
                in_position = False
                position_type = None
                equity_curve.append(equity_curve[-1] + positions[-1]['PnL'])
            # Stop loss: if price reverses to supertrend (close >= SL)
            elif candles['close'].values[i] >= positions[-1]['SL']:
                exit_idx = i+1
                exit_price = positions[-1]['SL'] + spread
                positions[-1]['Exit Time'] = candles['time'].values[exit_idx]
                positions[-1]['Exit Price'] = exit_price
                positions[-1]['PnL'] = (positions[-1]['Entry Price'] - exit_price) * lot_size
                in_position = False
                position_type = None
                equity_curve.append(equity_curve[-1] + positions[-1]['PnL'])

# If still in position at end, close at last close
if in_position:
    final_idx = len(candles)-1
    if position_type == 'long':
        exit_price = candles['close'].values[final_idx] - spread
        positions[-1]['Exit Time'] = candles['time'].values[final_idx]
        positions[-1]['Exit Price'] = exit_price
        positions[-1]['PnL'] = (exit_price - positions[-1]['Entry Price']) * lot_size
        equity_curve.append(equity_curve[-1] + positions[-1]['PnL'])
    elif position_type == 'short':
        exit_price = candles['close'].values[final_idx] + spread
        positions[-1]['Exit Time'] = candles['time'].values[final_idx]
        positions[-1]['Exit Price'] = exit_price
        positions[-1]['PnL'] = (positions[-1]['Entry Price'] - exit_price) * lot_size
        equity_curve.append(equity_curve[-1] + positions[-1]['PnL'])

# Calculate net pnl and max drawdown
net_pnl = sum([p['PnL'] for p in positions if p['PnL'] is not None])
equity_curve = np.array(equity_curve)
roll_max = np.maximum.accumulate(equity_curve)
drawdown = roll_max - equity_curve
max_drawdown = np.max(drawdown)

# Print positions table

# Format time columns for readability
def fmt_time(t):
    if pd.isnull(t):
        return ""
    if isinstance(t, (np.datetime64, pd.Timestamp)):
        return pd.to_datetime(t).strftime("%Y-%m-%d %H:%M:%S")
    try:
        return pd.to_datetime(t).strftime("%Y-%m-%d %H:%M:%S")
    except:
        return str(t)


# Add SL to position table
pos_table = []
for p in positions:
    pos_table.append([
        p['Type'],
        fmt_time(p['Entry Time']),
        p['Entry Price'],
        p['SL'],
        fmt_time(p['Exit Time']),
        p['Exit Price'],
        p['Size'],
        p['PnL']
    ])
pos_headers = ["Type", "Entry Time", "Entry Price", "SL", "Exit Time", "Exit Price", "Size", "PnL"]
print(tabulate(pos_table, headers=pos_headers, tablefmt="fancy_grid"))

# Print net pnl and max drawdown
print("\nNet PnL:", net_pnl)
print("Max Drawdown:", max_drawdown)
