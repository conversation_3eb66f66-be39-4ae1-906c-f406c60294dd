import MetaTrader5 as mt5
import pandas as pd
import numpy as np

# 1. Initialize MT5 connection
def init_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

# 2. Fetch historical bars into a DataFrame (5 days backtest)
def fetch_bars(symbol, timeframe, n_days=5):
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=n_days)
    rates = mt5.copy_rates_range(symbol, timeframe, utc_from, utc_to)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol} in the given period.")
    df = pd.DataFrame(rates)
    print(f"Fetched columns: {df.columns.tolist()}")  # Debug print
    # Always set 'volume' from tick_volume or real_volume if missing
    if 'volume' not in df.columns:
        if 'tick_volume' in df.columns:
            df['volume'] = df['tick_volume']
        elif 'real_volume' in df.columns:
            df['volume'] = df['real_volume']
        else:
            raise ValueError(f"No suitable volume column found. Columns: {df.columns.tolist()}")
    print(f"Rows after fetch: {len(df)}")
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"Returned data does not contain required column '{col}'. Columns: {df.columns.tolist()}")
    if 'time' not in df.columns:
        raise ValueError(f"Returned data does not contain 'time' column. Columns: {df.columns.tolist()}")
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    print(df.head())
    return df

# 3. Compute rolling metrics
def compute_indicators(df, atr_period=14, vol_ma=20):
    df['tr'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(np.abs(df['high'] - df['close'].shift()), np.abs(df['low'] - df['close'].shift()))
    )
    df['ATR'] = df['tr'].rolling(atr_period).mean()
    df['VolMA'] = df['volume'].rolling(vol_ma).mean()
    df['up_vol']   = ((df['close'] > df['open'])  & (df['volume'] > df['VolMA'])).astype(int)
    df['down_vol'] = ((df['close'] < df['open'])  & (df['volume'] > df['VolMA'])).astype(int)
    print(f"Rows after indicators: {len(df)}")
    print(df[['open','high','low','close','volume','ATR','VolMA']].head())
    return df

# 4. Identify range zones (potential accumulation/distribution)
def find_range_zones(df, window=15, atr_thresh=0.01, price_std_thresh=0.02):
    zones = []
    for i in range(window, len(df)):
        win = df.iloc[i-window:i]
        if win['ATR'].iloc[-1] / win['close'].iloc[-1] < atr_thresh \
           and win['close'].std() / win['close'].mean() < price_std_thresh:
            zones.append((win.index[0], win.index[-1]))
    merged = merge_zones(zones)
    print(f"Zones found: {len(merged)}")
    return merged

def merge_zones(zones):
    merged = []
    for start, end in sorted(zones):
        if not merged or start > merged[-1][1]:
            merged.append([start, end])
        else:
            merged[-1][1] = max(merged[-1][1], end)
    return merged

# 5. Classify zones as Accumulation, Manipulation, or Distribution
def classify_zones(df, zones, alpha=0.2, beta=0.4):
    labels = {}
    for start, end in zones:
        zone = df.loc[start:end]
        down_count = zone['down_vol'].sum()
        up_count = zone['up_vol'].sum()
        length = len(zone)
        # Accumulation: few big down bars, many big up bars
        if down_count < alpha * length and up_count > beta * length:
            labels[(start, end)] = 'ACCUMULATION'
        # Distribution: many big up bars, few big down bars
        elif up_count < alpha * length and down_count > beta * length:
            labels[(start, end)] = 'DISTRIBUTION'
        else:
            labels[(start, end)] = 'MANIPULATION'
    return labels

# 6. Tag bars with phase labels
def label_phases(df, zone_labels):
    df['Phase'] = 'TRADING'
    for (start, end), phase in zone_labels.items():
        df.loc[start:end, 'Phase'] = phase
    # Markup: bar right after an accumulation breakout
    for (start, end), phase in zone_labels.items():
        if phase == 'ACCUMULATION':
            breakout_idx = df.index.get_loc(end) + 1
            if breakout_idx < len(df):
                df.iloc[breakout_idx, df.columns.get_loc('Phase')] = 'MARKUP'
    # Markdown: bar right after a distribution breakdown
    for (start, end), phase in zone_labels.items():
        if phase == 'DISTRIBUTION':
            breakdown_idx = df.index.get_loc(end) + 1
            if breakdown_idx < len(df):
                df.iloc[breakdown_idx, df.columns.get_loc('Phase')] = 'MARKDOWN'
    return df

# 7. Print each group in separate tables
def print_zone_tables(df):
    found = False
    for phase in ['ACCUMULATION', 'MANIPULATION', 'DISTRIBUTION']:
        group = df[df['Phase'] == phase]
        if not group.empty:
            found = True
            print(f"\n{phase} ZONE TABLE:")
            print(group[['open','high','low','close','volume','Phase']])
    if not found:
        print("No accumulation, manipulation, or distribution zones found in the data.")

# 8. Main routine
def main():
    init_mt5()
    symbol    = "Volatility 100 (1s) Index"
    timeframe = mt5.TIMEFRAME_H1
    df = fetch_bars(symbol, timeframe, n_days=5)
    df = compute_indicators(df)
    zones = find_range_zones(df)
    zone_labels = classify_zones(df, zones)
    df = label_phases(df, zone_labels)
    print_zone_tables(df)
    mt5.shutdown()

if __name__ == "__main__":
    main()
