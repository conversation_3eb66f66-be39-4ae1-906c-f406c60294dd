import MetaTrader5 as mt5
import pandas as pd
import time

def init_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def fetch_latest_bars(symbol, n_bars=100):
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M3, 0, n_bars)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol}.")
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    return df

def calc_long_short_dir(df, n):
    bullish = (df['close'] > df['open'])
    long_dir_series = pd.Series(0, index=df.index)
    short_dir_series = pd.Series(0, index=df.index)
    long_dir_series[bullish] = (df['close'] - df['open']).abs()[bullish]
    short_dir_series[bullish] = 0
    short_dir_series[~bullish] = (df['close'] - df['open']).abs()[~bullish]
    long_dir_series[~bullish] = 0
    long_dir_sum = long_dir_series.rolling(window=n).sum()
    short_dir_sum = short_dir_series.rolling(window=n).sum()
    result_df = pd.DataFrame({
        'time': df.index,
        'long_dir': long_dir_sum,
        'short_dir': short_dir_sum
    })
    result_df = result_df.iloc[n:]
    return result_df.reset_index(drop=True)

def get_current_bid_ask(symbol):
    tick = mt5.symbol_info_tick(symbol)
    if tick is None:
        raise RuntimeError(f"No tick data for symbol {symbol}")
    return tick.bid, tick.ask

def send_order(symbol, direction, lot_size):
    bid, ask = get_current_bid_ask(symbol)
    price = ask if direction == 'long' else bid
    order_type = mt5.ORDER_TYPE_BUY if direction == 'long' else mt5.ORDER_TYPE_SELL
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": lot_size,
        "type": order_type,
        "price": price,
        "deviation": 10,
        "magic": 123456,
        "comment": "long_short_dir_live",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK
    }
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print(f"Order send failed: {result}")
        return None
    print(f"Order sent: {direction} at {price}")
    return result.order

def get_open_position(symbol):
    positions = mt5.positions_get(symbol=symbol)
    if positions:
        return positions[0]
    return None

def get_position_pnl(position):
    return position.profit if position else 0

def main():
    init_mt5()
    symbol = "Volatility 100 (1s) Index"
    lot_size = 0.7
    n = 1
    print("Starting live trading loop...")
    SL = 5.5
    TP = 1.5
    closed_positions = []
    position = None
    entry_time = None
    direction = None
    entry_price = None
    sl_price = None
    tp_price = None
    last_candle_time = None
    while True:
        df = fetch_latest_bars(symbol, n_bars=n+2)
        result_df = calc_long_short_dir(df, n)
        if len(result_df) < 2:
            time.sleep(1)
            continue
        long_dir_prev = result_df.iloc[-2]['long_dir']
        short_dir_prev = result_df.iloc[-2]['short_dir']
        long_dir = result_df.iloc[-1]['long_dir']
        short_dir = result_df.iloc[-1]['short_dir']
        time_prev = result_df.iloc[-2]['time']
        time_curr = result_df.iloc[-1]['time']
        # Only act when a new candle is detected
        if last_candle_time is None or time_curr != last_candle_time:
            last_candle_time = time_curr
            # Entry logic: use previous candle's signal for entry, execute at open of new candle
            if position is None:
                if long_dir_prev < short_dir_prev and long_dir > short_dir:
                    bid, ask = get_current_bid_ask(symbol)
                    entry_price = ask
                    entry_time = time_curr
                    direction = 'long'
                    sl_price = entry_price - SL
                    tp_price = entry_price + TP
                    order_id = send_order(symbol, direction, lot_size)
                    print(f"Long position opened at {entry_price} (time: {entry_time})")
                    position = get_open_position(symbol)
                elif short_dir_prev < long_dir_prev and short_dir > long_dir:
                    bid, ask = get_current_bid_ask(symbol)
                    entry_price = bid
                    entry_time = time_curr
                    direction = 'short'
                    sl_price = entry_price + SL
                    tp_price = entry_price - TP
                    order_id = send_order(symbol, direction, lot_size)
                    print(f"Short position opened at {entry_price} (time: {entry_time})")
                    position = get_open_position(symbol)
            else:
                # Exit logic: check SL/TP or signal reversal
                bid, ask = get_current_bid_ask(symbol)
                current_price = ask if direction == 'long' else bid
                exit_reason = None
                if direction == 'long':
                    if current_price <= sl_price:
                        exit_reason = 'SL'
                    elif current_price >= tp_price:
                        exit_reason = 'TP'
                    elif short_dir > long_dir:
                        exit_reason = 'Signal'
                elif direction == 'short':
                    if current_price >= sl_price:
                        exit_reason = 'SL'
                    elif current_price <= tp_price:
                        exit_reason = 'TP'
                    elif long_dir > short_dir:
                        exit_reason = 'Signal'
                if exit_reason:
                    close_order(symbol, position)
                    close_time = time_curr
                    pnl = get_position_pnl(position)
                    closed_positions.append({
                        "Entry Time": entry_time,
                        "Direction": direction,
                        "Entry Price": entry_price,
                        "Exit Time": close_time,
                        "Exit Price": current_price,
                        "PnL": pnl,
                        "Exit Reason": exit_reason
                    })
                    print(f"Position closed at {current_price} (time: {close_time}), Reason: {exit_reason}, PnL: {pnl}")
                    position = None
                    entry_time = None
                    direction = None
                    entry_price = None
                    sl_price = None
                    tp_price = None
                    # Print closed positions table
                    from tabulate import tabulate
                    print("\nClosed Positions Table:")
                    table = [[
                        pos["Entry Time"], pos["Direction"], pos["Entry Price"], pos["Exit Time"], pos["Exit Price"], pos["PnL"], pos["Exit Reason"]
                    ] for pos in closed_positions]
                    headers = ["Entry Time", "Direction", "Entry Price", "Exit Time", "Exit Price", "PnL", "Exit Reason"]
                    print(tabulate(table, headers=headers, tablefmt="fancy_grid"))
        time.sleep(1)
    mt5.shutdown()

def close_order(symbol, position):
    order_type = mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY
    volume = position.volume
    bid, ask = get_current_bid_ask(symbol)
    price = bid if order_type == mt5.ORDER_TYPE_SELL else ask
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": order_type,
        "position": position.ticket,
        "price": price,
        "deviation": 10,
        "magic": 123456,
        "comment": "long_short_dir_live_close",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK
    }
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print(f"Close order failed: {result}")
    else:
        print(f"Position closed at {price}")

if __name__ == "__main__":
    main()
