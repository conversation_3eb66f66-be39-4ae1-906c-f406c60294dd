


import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate





# Raw weights for micro/meso/macro
w1m_raw = np.array([0.045, 0.048, 0.051, 0.054, 0.057,
                   0.061, 0.064, 0.067, 0.070, 0.073,
                   0.076, 0.079, 0.082, 0.085, 0.088])
w5m_raw = np.array([0.303, 0.333, 0.364])
w15m_raw = 1.0

# Normalize weights so sum = 1 for each timeframe
w1m = w1m_raw / w1m_raw.sum()
w5m = w5m_raw / w5m_raw.sum()
w15m = 1.0

# Initial scales (will be optimized)
MICRO_SCALE = 1/3
MESO_SCALE = 1/3
MACRO_SCALE = 1/3

SIGNAL_THR = 0.0  # Force every candle to get a decision
SYMBOL = 'Volatility 100 (1s) Index'

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def fetch_bars(symbol, timeframe, count=20):
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
    if rates is None or len(rates) == 0:
        return pd.DataFrame()
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df[['time','open','high','low','close','tick_volume']]

def compute_atr(df, period=14):
    high_low = df['high'] - df['low']
    high_pc  = (df['high'] - df['close'].shift(1)).abs()
    low_pc   = (df['low']  - df['close'].shift(1)).abs()
    tr = pd.concat([high_low, high_pc, low_pc], axis=1).max(axis=1)
    atr = tr.ewm(alpha=1/period, adjust=False).mean()
    df = df.copy()
    df['tr'] = tr
    df['atr'] = atr
    return df

def score_last_15m_bar(df15, df5, df1, atr15, atr5, atr1, micro_scale=MICRO_SCALE, meso_scale=MESO_SCALE, macro_scale=MACRO_SCALE):
    # --- micro (1m) normalized returns ---
    r1 = (df1['close'].values - df1['open'].values) / (df1['open'].values * atr1)
    S_micro = np.dot(w1m, r1[-15:]) * micro_scale

    # --- meso (5m) normalized returns ---
    r5 = (df5['close'].values - df5['open'].values) / (df5['open'].values * atr5)
    S_meso = np.dot(w5m, r5) * meso_scale

    # --- macro (15m) normalized return ---
    r15 = (df15['close'] - df15['open']) / (df15['open'] * atr15)
    S_macro = w15m * r15 * macro_scale

    # composite score
    S_total = S_micro + S_meso + S_macro

    # decision (always LONG or SHORT)
    return ("LONG" if S_total > 0 else "SHORT"), S_total, S_micro, S_meso, S_macro

def main():
    initialize_mt5()
    # Fetch enough bars for last 2000 15m candles and their corresponding 5m and 1m bars
    num_15m = 2000
    num_5m = num_15m * 3 + 30  # 3x per 15m + extra for ATR
    num_1m = num_15m * 15 + 30 # 15x per 15m + extra for ATR
    df15_all = fetch_bars(SYMBOL, mt5.TIMEFRAME_M15, count=num_15m + 30)  # +30 for ATR window
    df5_all  = fetch_bars(SYMBOL, mt5.TIMEFRAME_M5,  count=num_5m)
    df1_all  = fetch_bars(SYMBOL, mt5.TIMEFRAME_M1,  count=num_1m)

    if df15_all.empty or df5_all.empty or df1_all.empty:
        print("Not enough data returned from MT5.")
        return

    # Prepare dataset for ML
    features = []
    labels = []
    times = []
    opens = []
    closes = []
    scores = []
    decisions = []

    # Loop over each 1m candle, build features from previous 15m, 3x5m, 15x1m candles
    for i in range(45, len(df1_all)):
        curr_1m = df1_all.iloc[i]
        t_curr = curr_1m['time']

        # Find previous 15m candle
        prev15 = df15_all[df15_all['time'] < t_curr].tail(1)
        if len(prev15) == 0:
            continue
        prev_df15 = prev15.iloc[0]
        prev15_idx = df15_all[df15_all['time'] == prev_df15['time']].index[0]

        # Previous 3x5m candles
        df5_prev = df5_all[df5_all['time'] < t_curr].tail(3)
        if len(df5_prev) < 3:
            continue

        # Previous 15x1m candles
        df1_prev = df1_all[df1_all['time'] < t_curr].tail(15)
        if len(df1_prev) < 15:
            continue

        # ATRs for each timeframe (use previous candles)
        atr15_df = compute_atr(df15_all.iloc[max(0,prev15_idx-30):prev15_idx+1], period=14)
        atr5_df  = compute_atr(df5_all[df5_all['time'] < t_curr].tail(20), period=14)
        atr1_df  = compute_atr(df1_all[df1_all['time'] < t_curr].tail(20), period=14)
        atr15 = atr15_df['atr'].iloc[-1]
        atr5  = atr5_df['atr'].iloc[-1]
        atr1  = atr1_df['atr'].iloc[-1]

        # --- micro (1m) normalized returns ---
        r1 = (df1_prev['close'].values - df1_prev['open'].values) / (df1_prev['open'].values * atr1)
        # --- meso (5m) normalized returns ---
        r5 = (df5_prev['close'].values - df5_prev['open'].values) / (df5_prev['open'].values * atr5)
        # --- macro (15m) normalized return ---
        r15 = (prev_df15['close'] - prev_df15['open']) / (prev_df15['open'] * atr15)

        # --- Advanced Feature Engineering ---
        feature_atrs = [atr1, atr5, atr15]
        prev_dir = np.sign(prev_df15['close'] - prev_df15['open'])
        if prev15_idx >= 14:
            ma15 = df15_all['close'].iloc[prev15_idx-14:prev15_idx+1].mean()
        else:
            ma15 = df15_all['close'].iloc[:prev15_idx+1].mean()
        ma15_diff = prev_df15['close'] - ma15

        lagged_returns = []
        for lag in range(1, 4):
            idx = prev15_idx - lag
            if idx >= 30:
                lag_atr_df = compute_atr(df15_all.iloc[max(0,idx-30):idx+1], period=14)
                lag_atr = lag_atr_df['atr'].iloc[-1]
                lagged_r = (df15_all.iloc[idx]['close'] - df15_all.iloc[idx]['open']) / (df15_all.iloc[idx]['open'] * lag_atr)
                lagged_returns.append(lagged_r)
            else:
                lagged_returns.append(0)

        if prev15_idx >= 4+30:
            window_returns = []
            for j in range(prev15_idx-4, prev15_idx+1):
                win_atr_df = compute_atr(df15_all.iloc[max(0,j-30):j+1], period=14)
                win_atr = win_atr_df['atr'].iloc[-1]
                window_returns.append((df15_all.iloc[j]['close'] - df15_all.iloc[j]['open']) / (df15_all.iloc[j]['open'] * win_atr))
            rolling_mean = np.mean(window_returns)
            rolling_std = np.std(window_returns)
        else:
            rolling_mean = 0
            rolling_std = 0
        rolling_returns = [rolling_mean, rolling_std]

        engulfing = 0
        if prev15_idx > 0:
            prev_body = abs(df15_all.iloc[prev15_idx-1]['close'] - df15_all.iloc[prev15_idx-1]['open'])
            curr_body = abs(prev_df15['close'] - prev_df15['open'])
            if curr_body > prev_body and np.sign(prev_df15['close'] - prev_df15['open']) != np.sign(df15_all.iloc[prev15_idx-1]['close'] - df15_all.iloc[prev15_idx-1]['open']):
                engulfing = 1
        pin_bar = 0
        body = abs(prev_df15['close'] - prev_df15['open'])
        upper_wick = prev_df15['high'] - max(prev_df15['close'], prev_df15['open'])
        lower_wick = min(prev_df15['close'], prev_df15['open']) - prev_df15['low']
        if (upper_wick > 2 * body) or (lower_wick > 2 * body):
            pin_bar = 1

        feature_vec = list(r1) + list(r5) + [r15] + feature_atrs + [prev_dir, ma15_diff] + lagged_returns + rolling_returns + [engulfing, pin_bar, 1.0]

        # Debug: print first 3 feature vectors and their length
        if i < 48:
            print(f"Feature vector sample {i} (len={len(feature_vec)}): {feature_vec}")

        S_micro = np.dot(w1m, r1) * MICRO_SCALE
        S_meso = np.dot(w5m, r5) * MESO_SCALE
        S_macro = w15m * r15 * MACRO_SCALE
        S_total = S_micro + S_meso + S_macro
        decision = "LONG" if S_total > 0 else "SHORT"

        # Label: 1 if current 1m candle moves in predicted direction, else 0
        next_move = curr_1m['close'] - curr_1m['open']
        label = 1 if (decision == "LONG" and next_move > 0) or (decision == "SHORT" and next_move < 0) else 0

        features.append(feature_vec)
        labels.append(label)
        times.append(curr_1m['time'])
        opens.append(curr_1m['open'])
        closes.append(curr_1m['close'])
        scores.append(S_total)
        decisions.append(decision)

    # Print training sample count, label distribution, and decision type counts
    print(f"\nTraining samples: {len(features)}")
    unique, counts = np.unique(labels, return_counts=True)
    print("Label distribution:", dict(zip(unique, counts)))
    from collections import Counter
    print("Decision type counts:", Counter(decisions))

    # Standardize features for better ML training
    def standardize(X):
        X = np.array(X)
        mean = X.mean(axis=0)
        std = X.std(axis=0)
        std[std == 0] = 1.0
        return (X - mean) / std, mean, std

    # Custom logistic regression (gradient descent) with L2 regularization
    def sigmoid(x):
        return 1 / (1 + np.exp(-x))

    def logistic_regression(X, y, lr=0.01, epochs=1000, l2=0.01):
        X = np.array(X)
        y = np.array(y)
        w = np.random.randn(X.shape[1]) * 0.01
        for epoch in range(epochs):
            z = np.dot(X, w)
            pred = sigmoid(z)
            grad = (np.dot(X.T, (pred - y)) / len(y)) + l2 * w
            w -= lr * grad
        return w

    if len(features) > 10 and len(set(labels)) > 1:
        # Train/test split for realistic evaluation
        split_idx = int(len(features) * 0.7)
        X_train_raw, y_train = np.array(features[:split_idx]), np.array(labels[:split_idx])
        X_test_raw, y_test = np.array(features[split_idx:]), np.array(labels[split_idx:])
        times_test = times[split_idx:]
        opens_test = opens[split_idx:]
        closes_test = closes[split_idx:]
        scores_test = scores[split_idx:]
        decisions_test = decisions[split_idx:]

        # Standardize features
        X_train, mean, std = standardize(X_train_raw)
        X_test = (X_test_raw - mean) / std

        # 1. Logistic Regression (already implemented)
        best_weights = logistic_regression(X_train, y_train, lr=0.05, epochs=2000, l2=0.01)
        best_weights = best_weights / np.sum(np.abs(best_weights))
        y_pred_lr = (np.dot(X_test, best_weights) > 0).astype(int)
        win_rate_lr = np.mean(y_pred_lr == y_test)

        # 2. Decision Tree (simple stump)
        def simple_decision_tree(X_train, y_train, X_test):
            # Decision stump: find best feature and threshold
            best_acc = 0
            best_feat = 0
            best_thr = 0
            for feat in range(X_train.shape[1]):
                thresholds = np.unique(X_train[:, feat])
                for thr in thresholds:
                    pred = (X_train[:, feat] > thr).astype(int)
                    acc = np.mean(pred == y_train)
                    if acc > best_acc:
                        best_acc = acc
                        best_feat = feat
                        best_thr = thr
            y_pred = (X_test[:, best_feat] > best_thr).astype(int)
            return y_pred
        y_pred_dt = simple_decision_tree(X_train, y_train, X_test)
        win_rate_dt = np.mean(y_pred_dt == y_test)

        # 3. Random Forest (ensemble of stumps)
        def simple_random_forest(X_train, y_train, X_test, n_estimators=10):
            np.random.seed(42)
            preds = []
            for _ in range(n_estimators):
                idx = np.random.choice(len(X_train), len(X_train)//2, replace=True)
                Xb = X_train[idx]
                yb = y_train[idx]
                pred = simple_decision_tree(Xb, yb, X_test)
                preds.append(pred)
            preds = np.array(preds)
            y_pred = (np.mean(preds, axis=0) > 0.5).astype(int)
            return y_pred
        y_pred_rf = simple_random_forest(X_train, y_train, X_test, n_estimators=10)
        win_rate_rf = np.mean(y_pred_rf == y_test)

        # 4. Simple Neural Network (MLP, 1 hidden layer)
        def simple_mlp(X_train, y_train, X_test, hidden_dim=16, epochs=200, lr=0.01):
            np.random.seed(42)
            input_dim = X_train.shape[1]
            W1 = np.random.randn(input_dim, hidden_dim) * 0.01
            b1 = np.zeros(hidden_dim)
            W2 = np.random.randn(hidden_dim) * 0.01
            b2 = 0.0
            def relu(x): return np.maximum(0, x)
            def sigmoid(x): return 1 / (1 + np.exp(-x))
            for epoch in range(epochs):
                Z1 = relu(np.dot(X_train, W1) + b1)
                Z2 = sigmoid(np.dot(Z1, W2) + b2)
                loss = -np.mean(y_train * np.log(Z2 + 1e-8) + (1 - y_train) * np.log(1 - Z2 + 1e-8))
                dZ2 = Z2 - y_train
                dW2 = np.dot(Z1.T, dZ2) / len(y_train)
                db2 = np.mean(dZ2)
                dZ1 = np.dot(dZ2.reshape(-1,1), W2.reshape(1,-1)) * (Z1 > 0)
                dW1 = np.dot(X_train.T, dZ1) / len(y_train)
                db1 = np.mean(dZ1, axis=0)
                W2 -= lr * dW2
                b2 -= lr * db2
                W1 -= lr * dW1
                b1 -= lr * db1
            # Predict
            Z1_test = relu(np.dot(X_test, W1) + b1)
            Z2_test = sigmoid(np.dot(Z1_test, W2) + b2)
            y_pred = (Z2_test > 0.5).astype(int)
            return y_pred
        y_pred_mlp = simple_mlp(X_train, y_train, X_test, hidden_dim=16, epochs=200, lr=0.01)
        win_rate_mlp = np.mean(y_pred_mlp == y_test)

        # Print model comparison table and highlight those above threshold
        print("\nModel Winning Rate Comparison:")
        from tabulate import tabulate
        WINNING_RATE_THR = 0.95
        model_table = [
            ["Logistic Regression", f"{win_rate_lr*100:.2f}%", "✔" if win_rate_lr >= WINNING_RATE_THR else "✘"],
            ["Decision Tree", f"{win_rate_dt*100:.2f}%", "✔" if win_rate_dt >= WINNING_RATE_THR else "✘"],
            ["Random Forest", f"{win_rate_rf*100:.2f}%", "✔" if win_rate_rf >= WINNING_RATE_THR else "✘"],
            ["Neural Net (MLP)", f"{win_rate_mlp*100:.2f}%", "✔" if win_rate_mlp >= WINNING_RATE_THR else "✘"],
        ]
        print(tabulate(model_table, headers=["Model", "Winning Rate", "95%+"], tablefmt="fancy_grid"))

        # Print best model
        win_rates = [win_rate_lr, win_rate_dt, win_rate_rf, win_rate_mlp]
        model_names = ["Logistic Regression", "Decision Tree", "Random Forest", "Neural Net (MLP)"]
        best_idx = np.argmax([wr if wr is not None else 0 for wr in win_rates])
        print(f"\nBest Model: {model_names[best_idx]} with Winning Rate: {win_rates[best_idx]*100:.2f}%")

        # Print weights for logistic regression only
        print("\nOptimized ML weights (Logistic Regression):")
        print("  1 min (micro) weights:")
        for idx, w in enumerate(best_weights[:15]):
            print(f"    Candle {idx+1}: {w:.6f}")
        print("  5 min (meso) weights:")
        for idx, w in enumerate(best_weights[15:18]):
            print(f"    Candle {idx+1}: {w:.6f}")
        print(f" 15 min (macro) weight: {best_weights[18]:.6f}")
        print(f"  ATR weights: {best_weights[19]:.6f}, {best_weights[20]:.6f}, {best_weights[21]:.6f}")
        print(f"  Prev dir weight: {best_weights[22]:.6f}")
        print(f"  MA diff weight: {best_weights[23]:.6f}")
        print(f"  Bias term: {best_weights[24]:.6f}")
    else:
        # Default: evenly split
        best_weights = np.concatenate([
            np.full(15, MICRO_SCALE/15),
            np.full(3, MESO_SCALE/3),
            np.array([MACRO_SCALE])
        ])
        print("\nNot enough data to train ML model, using default weights.")
        print("  1 min (micro) weights:")
        for idx, w in enumerate(best_weights[:15]):
            print(f"    Candle {idx+1}: {w:.6f}")
        print("  5 min (meso) weights:")
        for idx, w in enumerate(best_weights[15:18]):
            print(f"    Candle {idx+1}: {w:.6f}")
        print(f" 15 min (macro) weight: {best_weights[18]:.6f}")

    # Print table and winning rate for test set only
    if len(features) > 10 and len(set(labels)) > 1:
        table = []
        win_count = 0
        for i in range(len(X_test)):
            ml_score = np.dot(X_test[i], best_weights)
            ml_decision = "NO_TRADE" if abs(ml_score) < SIGNAL_THR else ("LONG" if ml_score > 0 else "SHORT")
            if i < len(X_test) - 1:
                actual = 'UP' if closes_test[i] < closes_test[i+1] else 'DOWN' if closes_test[i] > closes_test[i+1] else 'FLAT'
            else:
                actual = 'N/A'
            win_count += y_test[i]
            table.append({
                'time': times_test[i],
                'open': opens_test[i],
                'close': closes_test[i],
                'score': ml_score,
                'decision': ml_decision,
                'actual': actual,
                'success': '✔' if y_test[i] == 1 else '✘'
            })
        print(tabulate(table[-100:], headers='keys', tablefmt='fancy_grid', showindex=False))
        win_rate = win_count / len(X_test) if len(X_test) > 0 else 0
        print(f"\nWinning rate (test set): {win_rate:.2%} (successes: {win_count} / total: {len(X_test)})")
    else:
        table = []
        win_count = 0
        for i in range(len(features)):
            ml_score = np.dot(features[i], best_weights)
            ml_decision = "NO_TRADE" if abs(ml_score) < SIGNAL_THR else ("LONG" if ml_score > 0 else "SHORT")
            if i < len(features) - 1:
                actual = 'UP' if closes[i] < closes[i+1] else 'DOWN' if closes[i] > closes[i+1] else 'FLAT'
            else:
                actual = 'N/A'
            win_count += labels[i]
            table.append({
                'time': times[i],
                'open': opens[i],
                'close': closes[i],
                'score': ml_score,
                'decision': ml_decision,
                'actual': actual,
                'success': '✔' if labels[i] == 1 else '✘'
            })
        print(tabulate(table[-100:], headers='keys', tablefmt='fancy_grid', showindex=False))
        win_rate = win_count / len(features) if len(features) > 0 else 0
        print(f"\nWinning rate: {win_rate:.2%} (successes: {win_count} / total: {len(features)})")

if __name__ == "__main__":
    main()

