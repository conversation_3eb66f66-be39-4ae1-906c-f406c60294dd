
import MetaTrader5 as mt5
import pandas as pd

def main():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")
    symbol = "Volatility 100 (1s) Index"
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=60)
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, utc_from, utc_to)
    if rates is None or len(rates) == 0:
        print(f"No data returned for symbol {symbol} in the given period.")
    else:
        df = pd.DataFrame(rates)
        print(df)
    mt5.shutdown()

if __name__ == "__main__":
    main()
