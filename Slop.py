import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from math import atan2, degrees
from tabulate import tabulate

# 1. Initialize MT5 connection
def init_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

# 2. Fetch historical bars into a DataFrame (1 day)
def fetch_bars(symbol, timeframe, n_days=1):
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=n_days)
    rates = mt5.copy_rates_range(symbol, timeframe, utc_from, utc_to)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol} in the given period.")
    df = pd.DataFrame(rates)
    # Always set 'volume' from tick_volume or real_volume if missing
    if 'volume' not in df.columns:
        if 'tick_volume' in df.columns:
            df['volume'] = df['tick_volume']
        elif 'real_volume' in df.columns:
            df['volume'] = df['real_volume']
        else:
            raise ValueError(f"No suitable volume column found. Columns: {df.columns.tolist()}")
    if 'time' not in df.columns:
        raise ValueError(f"Returned data does not contain 'time' column. Columns: {df.columns.tolist()}")
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    return df

# 3. Calculate high-to-high and low-to-low slope angles
def calculate_slopes(df):
    results = []
    times = df.index.to_list()
    for i in range(len(df) - 1):
        t1, t2 = times[i], times[i+1]
        high1, high2 = df.iloc[i]['high'], df.iloc[i+1]['high']
        low1, low2 = df.iloc[i]['low'], df.iloc[i+1]['low']
        dx = 1  # Use 1 bar as the x-axis difference for visual chart angle
        high_angle = degrees(atan2(high2 - high1, dx))
        low_angle = degrees(atan2(low2 - low1, dx))
        results.append([
            t1,
            t2,
            round(high_angle, 6),
            round(low_angle, 6)
        ])
    return results

# 4. Main routine
def main():
    init_mt5()
    symbol = "Volatility 100 (1s) Index"
    timeframe = mt5.TIMEFRAME_M1
    df = fetch_bars(symbol, timeframe, n_days=1)
    slope_table = calculate_slopes(df)
    headers = ['nth candle time', 'n+1th candle time', 'high to high angle (deg)', 'low to low angle (deg)']
    print("\nSLOPE ANGLE TABLE:")
    print(tabulate(slope_table, headers=headers, tablefmt="github", floatfmt=".2f"))
    mt5.shutdown()

if __name__ == "__main__":
    main()
