
import MetaTrader5 as mt5
import pandas as pd

def init_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def fetch_m3_bars(symbol, days=10):
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=days)
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, utc_from, utc_to)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol} in the given period.")
    df = pd.DataFrame(rates)
    if 'time' not in df.columns:
        raise ValueError(f"Returned data does not contain 'time' column. Columns: {df.columns.tolist()}")
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    return df

def calc_long_short_dir(df, n):
    # Vectorized calculation for long_dir and short_dir
    bullish = (df['close'] > df['open'])
    long_dir_series = pd.Series(0, index=df.index)
    short_dir_series = pd.Series(0, index=df.index)
    long_dir_series[bullish] = (df['low'] - df['close']).abs()[bullish]
    short_dir_series[bullish] = (df['high'] - df['close']).abs()[bullish]
    short_dir_series[~bullish] = (df['high'] - df['close']).abs()[~bullish]
    long_dir_series[~bullish] = (df['low'] - df['close']).abs()[~bullish]

    long_dir_sum = long_dir_series.rolling(window=n).sum()
    short_dir_sum = short_dir_series.rolling(window=n).sum()

    result_df = pd.DataFrame({
        'time': df.index,
        'long_dir': long_dir_sum,
        'short_dir': short_dir_sum
    })
    result_df = result_df.iloc[n:]
    return result_df.reset_index(drop=True)

def main():
    init_mt5()
    symbol = "Volatility 100 (1s) Index"
    try:
        n = int(input("Enter period n for calculation (e.g. 41): "))
    except Exception:
        print("Invalid input. Using default n=41.")
        n = 41
    df = fetch_m3_bars(symbol)
    result_df = calc_long_short_dir(df, n)
    print(result_df)
    mt5.shutdown()

if __name__ == "__main__":
    main()
