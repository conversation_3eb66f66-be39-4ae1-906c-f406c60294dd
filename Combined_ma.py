

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta > 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def gauss(x, h):
    return np.exp(-(x ** 2) / (h * h * 2))

def nadaraya_watson_envelope(src, h=8.0, mult=3.0, window=500):
    n = len(src)
    # Precompute Gaussian weights for endpoint method
    coefs = np.array([np.exp(-(i ** 2) / (h * h * 2)) for i in range(window)])
    coefs = coefs[::-1]  # reverse to match src[-window:]
    den = np.sum(coefs)
    out = np.full(n, np.nan)
    for i in range(window, n):
        values = src[i-window:i]
        out[i] = np.sum(values * coefs) / den
    # MAE as rolling mean of abs(src - out)
    mae = pd.Series(np.abs(src - out)).rolling(window, min_periods=1).mean() * mult
    upper = out + mae
    lower = out - mae
    return out, upper, lower

def main():
    initialize_mt5()
    symbol = 'Volatility 100 (1s) Index'
    lower_tf_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M3, 0, 10000)
    if lower_tf_rates is None or len(lower_tf_rates) == 0:
        print(f"No data returned from MT5 for {symbol}.")
        return
    df = pd.DataFrame(lower_tf_rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    src = df['close'].values
    out, upper, lower = nadaraya_watson_envelope(src, h=10.0, mult=3.0, window=500)
    df['NWE'] = out
    df['Upper'] = upper
    df['Lower'] = lower
    print(tabulate(df[['time', 'close', 'NWE', 'Upper', 'Lower']], headers='keys', tablefmt='fancy_grid', showindex=False))

    # Filter and print where close is above Upper or below Lower
    df_signal = df[(df['close'] > df['Upper']) | (df['close'] < df['Lower'])].copy()
    df_signal['Position'] = np.where(df_signal['close'] > df_signal['Upper'], 'Above', 'Below')
    if not df_signal.empty:
        print("\nCandles where close is above Upper or below Lower:")
        print(tabulate(df_signal[['time', 'close', 'NWE', 'Upper', 'Lower', 'Position']], headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print("\nNo candles found where close is above Upper or below Lower.")

if __name__ == "__main__":
    main()

