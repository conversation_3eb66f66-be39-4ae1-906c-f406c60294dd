import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def convert_to_heikin_ashi(df):
    ha_df = df.copy()
    ha_close = (df['open'] + df['high'] + df['low'] + df['close']) / 4
    ha_open = [df['open'].iloc[0]]
    for i in range(1, len(df)):
        ha_open.append((ha_open[i-1] + ha_close.iloc[i-1]) / 2)
    ha_df['HA_open'] = ha_open
    ha_df['HA_close'] = ha_close
    ha_df['HA_high'] = pd.DataFrame({'h': df['high'], 'ho': ha_df['HA_open'], 'hc': ha_df['HA_close']}).max(axis=1)
    ha_df['HA_low'] = pd.DataFrame({'l': df['low'], 'ho': ha_df['HA_open'], 'hc': ha_df['HA_close']}).min(axis=1)
    return ha_df

class TradePosition:
    def __init__(self, symbol, entry_index, entry_timestamp, entry_trade_price, trade_direction, entry_rsi_value, entry_reason):
        self.symbol = symbol
        self.entry_index = entry_index
        self.entry_timestamp = entry_timestamp
        self.entry_trade_price = entry_trade_price
        self.trade_direction = trade_direction  # 'long' or 'short'
        self.entry_rsi_value = entry_rsi_value
        self.exit_index = None
        self.exit_timestamp = None
        self.exit_trade_price = None
        self.exit_rsi_value = None
        self.trade_pnl = None
        self.entry_reason = entry_reason

    def close(self, exit_index, exit_timestamp, exit_trade_price, exit_rsi_value, trade_pnl, close_reason):
        self.exit_index = exit_index
        self.exit_timestamp = exit_timestamp
        self.exit_trade_price = exit_trade_price
        self.exit_rsi_value = exit_rsi_value
        self.trade_pnl = trade_pnl
        self.close_reason = close_reason

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta < 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    # ...existing code...


def main():
    initialize_mt5()
    symbols = [
       
        "DEX 1500 DOWN Index"
        
    ]
    from datetime import datetime, timedelta
    utc_now = datetime.now()
    utc_start = utc_now - timedelta(days=300)

    # Prepare M5 data for all symbols
    symbol_data = {}
    for symbol in symbols:
        m5_rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M30, utc_start, utc_now)
        if m5_rates is None or len(m5_rates) == 0:
            print(f"No M5 data returned from MT5 for {symbol}. Skipping this symbol.")
            continue
        df_m5 = pd.DataFrame(m5_rates)
        df_m5['time'] = pd.to_datetime(df_m5['time'], unit='s')
        # Remove the last (possibly incomplete) M5 candle so only fully closed candles are used
        df_m5 = df_m5.iloc[:-1] if len(df_m5) > 0 else df_m5
        # Convert to Heikin Ashi candles
        df_m5 = convert_to_heikin_ashi(df_m5)
        # Use HA_close for RSI and EMA calculations
        df_m5['RSI'] = calculate_rsi(df_m5['HA_close'], rsi_period=7)
        df_m5['EMA50'] = df_m5['HA_close'].ewm(span=50, adjust=False).mean()
        symbol_data[symbol] = df_m5

    lot_size_sequence = [0.2,0.1]
    lot_size_index = 0
    current_lot_size = lot_size_sequence[lot_size_index]
    trade_spread = 0.0
    take_profit = 7.3
    stop_loss = 3.5
    executed_positions = []
    active_position = None

    nan_skipped_bars = 0
    rsi_cross_above_30 = 0
    rsi_cross_below_70 = 0
    missed_long_signals = 0
    missed_short_signals = 0
    rsi_printed_count = 0

    # Merge all symbol M5 data into a single time-ordered list of bars
    merged_bars = []
    for symbol in symbols:
        df_m5 = symbol_data[symbol]
        for i in range(1, len(df_m5)):
            curr_bar = df_m5.iloc[i]
            prev_bar = df_m5.iloc[i-1]
            merged_bars.append({
                'symbol': symbol,
                'index': i,
                'curr_bar': curr_bar,
                'prev_bar': prev_bar
            })
    # Sort merged bars by time
    merged_bars.sort(key=lambda x: x['curr_bar']['time'])
    from tabulate import tabulate
    dashboard_rows = []
    for symbol in symbols:
        df = symbol_data[symbol]
        # Use the 9th last row (last closed candle is -2, so 9th last is -9)
        if len(df) < 10:
            curr_bar = df.iloc[0]
            prev_bar = curr_bar
        else:
            curr_bar = df.iloc[-9]
            prev_bar = df.iloc[-10] if len(df) > 10 else curr_bar
        rsi = round(curr_bar['RSI'], 2) if not pd.isna(curr_bar['RSI']) else None
        ema50 = round(curr_bar['EMA50'], 2) if not pd.isna(curr_bar['EMA50']) else None
        close = round(curr_bar['HA_close'], 2) if not pd.isna(curr_bar['HA_close']) else None
        prev_rsi = prev_bar['RSI'] if not pd.isna(prev_bar['RSI']) else None
        time_str = str(curr_bar['time']) if 'time' in curr_bar and not pd.isna(curr_bar['time']) else "-"
        # Long entry conditions
        rsi_cross_long = prev_rsi is not None and rsi is not None and prev_rsi < 30 and rsi >= 30
        ema_long = close is not None and ema50 is not None and close < ema50
        # Short entry conditions
        rsi_cross_short = prev_rsi is not None and rsi is not None and prev_rsi > 70 and rsi <= 70
        ema_short = close is not None and ema50 is not None and close > ema50
        entry_long = rsi_cross_long and ema_long
        entry_short = rsi_cross_short and ema_short
        entry_signal = "Long" if entry_long else ("Short" if entry_short else "-")
        dashboard_rows.append([
            symbol,
            time_str,
            f"{rsi:.2f}" if rsi is not None else "-",
            f"{ema50:.2f}" if ema50 is not None else "-",
                f"{close:.2f}" if close is not None else "-",
            str(rsi_cross_long),
            str(ema_long),
            str(rsi_cross_short),
            str(ema_short),
            entry_signal
        ])
    print(tabulate(
        dashboard_rows,
        headers=["Symbol", "Time", "RSI_lower", "RSI_higher", "EMA15_lower", "Close", "RSI Cross", "M5 RSI Filter", "EMA Filter", "Entry Signal?"],
        tablefmt="psql"
    ))

    bar_idx = 0
    while bar_idx < len(merged_bars):
        if active_position is not None:
            # Only process exit logic if a position is open
            symbol = active_position.symbol
            df_m5 = symbol_data[symbol]
            i = active_position.entry_index + 1
            while i < len(df_m5):
                curr_bar = df_m5.iloc[i]
                close_price = curr_bar['HA_close']
                curr_ema50 = curr_bar['EMA50']
                curr_rsi = curr_bar['RSI']
                trade_direction = active_position.trade_direction
                entry_trade_price = active_position.entry_trade_price
                exit_trade_price = close_price
                if trade_direction == 'long':
                    sl_hit = close_price - entry_trade_price <= -stop_loss
                    tp_hit = close_price - entry_trade_price >= take_profit
                    if sl_hit:
                        exit_trade_price -= trade_spread
                        trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi, trade_pnl, f'SL -{stop_loss}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        bar_idx += 1  # Skip to next bar after closing position
                        break
                    elif tp_hit:
                        exit_trade_price -= trade_spread
                        trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi, trade_pnl, f'TP +{take_profit}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        bar_idx += 1  # Skip to next bar after closing position
                        break
                elif trade_direction == 'short':
                    sl_hit = entry_trade_price - close_price <= -stop_loss
                    tp_hit = entry_trade_price - close_price >= take_profit
                    if sl_hit:
                        exit_trade_price += trade_spread
                        trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi, trade_pnl, f'SL +{stop_loss}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        bar_idx += 1  # Skip to next bar after closing position
                        break
                    elif tp_hit:
                        exit_trade_price += trade_spread
                        trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi, trade_pnl, f'TP -{take_profit}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        bar_idx += 1  # Skip to next bar after closing position
                        break
                i += 1
            # If position is still open at the end of symbol data, close it
            if active_position:
                curr_bar = df_m5.iloc[-1]
                close_price = curr_bar['HA_close']
                curr_rsi = curr_bar['RSI']
                if trade_direction == 'long':
                    exit_trade_price = close_price - trade_spread
                    trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                    active_position.close(len(df_m5)-1, curr_bar['time'], exit_trade_price, curr_rsi, trade_pnl, 'End of data')
                else:
                    exit_trade_price = close_price + trade_spread
                    trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                    active_position.close(len(df_m5)-1, curr_bar['time'], exit_trade_price, curr_rsi, trade_pnl, 'End of data')
                executed_positions.append(active_position)
                if trade_pnl > 0:
                    if lot_size_index < len(lot_size_sequence) - 1:
                        lot_size_index += 1
                elif trade_pnl < 0:
                    lot_size_index = 0
                current_lot_size = lot_size_sequence[lot_size_index]
                active_position = None
            continue  # After exit, go to next bar
        # Only check for new position if none is open
        bar = merged_bars[bar_idx]
        symbol = bar['symbol']
        i = bar['index']
        curr_bar = bar['curr_bar']
        prev_bar = bar['prev_bar']
        close_price = curr_bar['HA_close']
        time_val = curr_bar['time']
        prev_rsi = prev_bar['RSI']
        curr_rsi = curr_bar['RSI']
        curr_ema50 = curr_bar['EMA50']
        # Skip bars with NaN RSI or EMA values
        if pd.isna(prev_rsi) or pd.isna(curr_rsi) or pd.isna(curr_ema50):
            nan_skipped_bars += 1
            bar_idx += 1
            # Removed invalid continue outside loop
            continue
        # Count RSI crosses
        if prev_rsi < 30 and curr_rsi >= 30:
            rsi_cross_above_30 += 1
        if prev_rsi > 70 and curr_rsi <= 70:
            rsi_cross_below_70 += 1
        is_long_entry = (prev_rsi < 30 and curr_rsi >= 30 and close_price < curr_ema50)
        is_short_entry = (prev_rsi > 70 and curr_rsi <= 70 and close_price > curr_ema50)
        # Only allow new entry if no active position for this symbol
        if active_position is None:
            if is_long_entry or is_short_entry:
                open_for_symbol = (
                    (active_position is not None and active_position.symbol == symbol)
                    or any(pos for pos in executed_positions if pos.symbol == symbol and pos.exit_timestamp is None)
                )
                if not open_for_symbol:
                    # Entry on next candle's open
                    next_idx = i + 1
                    df_m5_signal = symbol_data[symbol]
                    if next_idx < len(df_m5_signal):
                        next_bar = df_m5_signal.iloc[next_idx]
                        entry_time = next_bar['time']
                        entry_open = next_bar['open']  # Use Japanese candle open for entry
                        entry_rsi = next_bar['RSI']
                        if is_long_entry:
                            entry_trade_price = entry_open + trade_spread
                            active_position = TradePosition(symbol, next_idx, entry_time, entry_trade_price, 'long', entry_rsi, 'Long entry (RSI cross, next open)')
                        else:
                            entry_trade_price = entry_open - trade_spread
                            active_position = TradePosition(symbol, next_idx, entry_time, entry_trade_price, 'short', entry_rsi, 'Short entry (RSI cross, next open)')
                        bar_idx += 1
                    else:
                        # No next candle, cannot enter
                        bar_idx += 1
        bar_idx += 1
        # ...existing code...
    # ...existing code...


    # ...existing code...

    # All positions are closed in the main loop above

    # Prepare results table
    if executed_positions:
        results = []
        net_pnl = 0.0
        profitable_count = 0
        loss_count = 0
        # For datewise stats
        date_stats = {}
        for pos in executed_positions:
            entry_date = pos.entry_timestamp.date() if hasattr(pos.entry_timestamp, 'date') else pd.to_datetime(pos.entry_timestamp).date()
            pnl = pos.trade_pnl if pos.trade_pnl is not None else 0.0
            is_profit = pnl > 0
            is_loss = pnl < 0
            if entry_date not in date_stats:
                date_stats[entry_date] = {'pnl': 0.0, 'profit_trades': 0, 'loss_trades': 0}
            date_stats[entry_date]['pnl'] += pnl
            if is_profit:
                date_stats[entry_date]['profit_trades'] += 1
            elif is_loss:
                date_stats[entry_date]['loss_trades'] += 1

            results.append({
                'Symbol': pos.symbol,
                'Entry Time': pos.entry_timestamp,
                'Entry Price': round(pos.entry_trade_price, 2),
                'Entry RSI': round(pos.entry_rsi_value, 2),
                'Direction': pos.trade_direction,
                'Exit Time': pos.exit_timestamp,
                'Exit Price': round(pos.exit_trade_price, 2) if pos.exit_trade_price is not None else None,
                'Exit RSI': round(pos.exit_rsi_value, 2) if pos.exit_rsi_value is not None else None,
                'PnL': round(pos.trade_pnl, 2) if pos.trade_pnl is not None else None,
                'Reason': getattr(pos, 'close_reason', None)
            })
            if pos.trade_pnl is not None:
                net_pnl += pos.trade_pnl
                if pos.trade_pnl > 0:
                    profitable_count += 1
                elif pos.trade_pnl < 0:
                    loss_count += 1
        print(tabulate(results, headers='keys', tablefmt='psql', showindex=False))
        print(f"\nNet PnL: {round(net_pnl, 2)}")
        print(f"Profitable positions: {profitable_count}")
        print(f"Loss positions: {loss_count}")

        # Print datewise PnL table
        datewise_table = []
        for date, stats in sorted(date_stats.items()):
            datewise_table.append({
                'Date': date,
                'Total PnL': round(stats['pnl'], 2),
                'Profitable Trades': stats['profit_trades'],
                'Loss Trades': stats['loss_trades']
            })
        print("\nDatewise PnL Table:")
        print(tabulate(datewise_table, headers='keys', tablefmt='psql', showindex=False))

        # Calculate max drawdown
        equity_curve = []
        cumulative = 0.0
        for pos in executed_positions:
            if pos.trade_pnl is not None:
                cumulative += pos.trade_pnl
            equity_curve.append(cumulative)
        max_drawdown = 0.0
        peak = float('-inf')
        for eq in equity_curve:
            if eq > peak:
                peak = eq
            drawdown = peak - eq
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        print(f"Max Drawdown: {round(max_drawdown, 2)}")
    else:
        print('No trades executed.')
    mt5.shutdown()

if __name__ == "__main__":
    main()
