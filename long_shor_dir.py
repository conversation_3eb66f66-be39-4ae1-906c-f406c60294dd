
import MetaTrader5 as mt5
import pandas as pd
from tabulate import tabulate

def init_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def fetch_m3_bars(symbol, days=30):
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=days)
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M3, utc_from, utc_to)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol} in the given period.")
    df = pd.DataFrame(rates)
    if 'time' not in df.columns:
        raise ValueError(f"Returned data does not contain 'time' column. Columns: {df.columns.tolist()}")
    df['time'] = pd.to_datetime(df['time'], unit='s')
    # Check for duplicate timestamps
    dupes = df['time'].duplicated(keep=False)
    if dupes.any():
        print(f"Warning: {dupes.sum()} duplicate timestamps found in price data. Dropping duplicates, keeping first occurrence.")
        df = df[~dupes | ~df.duplicated('time', keep='first')]
    df.set_index('time', inplace=True)
    return df

def calc_long_short_dir(df, n):
    # Modified calculation for long_dir and short_dir
    bullish = (df['close'] > df['open'])
    long_dir_series = pd.Series(0, index=df.index)
    short_dir_series = pd.Series(0, index=df.index)
    long_dir_series[bullish] = (df['close'] - df['open']).abs()[bullish]
    short_dir_series[bullish] = 0
    short_dir_series[~bullish] = (df['close'] - df['open']).abs()[~bullish]
    long_dir_series[~bullish] = 0

    long_dir_sum = long_dir_series.rolling(window=n).sum()
    short_dir_sum = short_dir_series.rolling(window=n).sum()

    result_df = pd.DataFrame({
        'time': df.index,
        'long_dir': long_dir_sum,
        'short_dir': short_dir_sum
    })
    result_df = result_df.iloc[n:]
    return result_df.reset_index(drop=True)

def main():
    init_mt5()
    symbol = "Boom 1000 Index"
    try:
        n = int(input("Enter period n for calculation (e.g. 1): "))
    except Exception:
        print("Invalid input. Using default n=1.")
        n = 1
    df = fetch_m3_bars(symbol)
    result_df = calc_long_short_dir(df, n)
    positions = backtest_long_short_cross(df, result_df, lot_size=0.7, spread=0.0)
    # No filter for positions with entry and exit on the same candle
    # Print summary of all actual entry times and signal times for cross-checking
    print("\n=== ENTRY SUMMARY ===")
    entry_summary = []
    time_to_idx = {row['time']: idx for idx, row in result_df.iterrows()}
    for pos in positions:
        entry_time = pos['entry_time']
        signal_time = None
        direction = pos['direction']
        if entry_time is not None and entry_time in time_to_idx:
            idx = time_to_idx[entry_time]
            if idx > 0:
                signal_time = result_df.loc[idx-1, 'time']
        entry_summary.append((entry_time, signal_time, direction))
    for entry_time, signal_time, direction in entry_summary:
        print(f"Entry: {entry_time} | Signal: {signal_time} | Direction: {direction}")

    # ...existing code for table...
    table = []
    for pos in positions:
        entry_time = pos['entry_time']
        signal_time = None
        long_dir_val = None
        short_dir_val = None
        prev_long_dir_val = None
        prev_short_dir_val = None
        if entry_time is not None and entry_time in time_to_idx:
            idx = time_to_idx[entry_time]
            if idx > 0:
                signal_time = result_df.loc[idx-1, 'time']
                prev_long_dir_val = result_df.loc[idx-1, 'long_dir']
                prev_short_dir_val = result_df.loc[idx-1, 'short_dir']
            long_dir_val = result_df.loc[idx, 'long_dir']
            short_dir_val = result_df.loc[idx, 'short_dir']
        table.append([
            pos['entry_time'],
            signal_time,
            pos['direction'],
            round(pos['entry_price'], 5),
            long_dir_val,
            short_dir_val,
            prev_long_dir_val,
            prev_short_dir_val,
            pos['exit_time'],
            round(pos['exit_price'], 5) if pos['exit_price'] is not None else None,
            round(pos['pnl'], 2) if pos['pnl'] is not None else None,
            pos.get('exit_reason', '')
        ])
    headers = ["Entry Time", "Signal Time", "Direction", "Entry Price", "Long Dir", "Short Dir", "Prev Long Dir", "Prev Short Dir", "Exit Time", "Exit Price", "PnL", "Exit Reason"]
    print(tabulate(table, headers=headers, tablefmt="fancy_grid"))

    # Datewise PnL table
    from collections import defaultdict
    date_pnl = defaultdict(float)
    for pos in positions:
        if pos['pnl'] is not None and pos['exit_time'] is not None:
            date = str(pos['exit_time']).split()[0]
            date_pnl[date] += pos['pnl']
    date_table = []
    for date in sorted(date_pnl.keys()):
        date_table.append([date, round(date_pnl[date], 2)])
    print("\nDatewise PnL:")
    print(tabulate(date_table, headers=["Date", "PnL"], tablefmt="fancy_grid"))

    # Profitable and loss positions
    profitable = sum(1 for pos in positions if pos['pnl'] is not None and pos['pnl'] > 0)
    loss = sum(1 for pos in positions if pos['pnl'] is not None and pos['pnl'] < 0)
    print(f"\nNumber of Profitable Positions: {profitable}")
    print(f"Number of Loss Positions: {loss}")

    # Net PnL
    net_pnl = sum(pos['pnl'] for pos in positions if pos['pnl'] is not None)
    print(f"\nNet PnL: {round(net_pnl, 2)}")

    # Max Drawdown calculation
    equity_curve = []
    equity = 0
    for pos in positions:
        if pos['pnl'] is not None:
            equity += pos['pnl']
            equity_curve.append(equity)
    max_drawdown = 0
    peak = 0
    for eq in equity_curve:
        if eq > peak:
            peak = eq
        dd = peak - eq
        if dd > max_drawdown:
            max_drawdown = dd
    print(f"Max Drawdown: {round(max_drawdown, 2)}")
    mt5.shutdown()
def backtest_long_short_cross(df, result_df, lot_size=0.7, spread=0.0):
    # Calculate EMA25 on close prices
    df['ema25'] = df['close'].ewm(span=25, adjust=False).mean()
    positions = []
    position = None
    entry_idx = None
    entry_price = None
    entry_time = None
    direction = None
    SL = 5.5
    TP = 1.5
    i = 1
    while i < len(result_df) - 1:  # ensure i+1 is valid
        long_dir_prev = result_df.loc[i-1, 'long_dir']
        short_dir_prev = result_df.loc[i-1, 'short_dir']
        long_dir = result_df.loc[i, 'long_dir']
        short_dir = result_df.loc[i, 'short_dir']
        time_prev = result_df.loc[i-1, 'time']
        time_curr = result_df.loc[i, 'time']
        time_next = result_df.loc[i+1, 'time']
        price_row_next = df.loc[time_next] if time_next in df.index else None
        if price_row_next is None:
            i += 1
            continue
        open_price_next = price_row_next['open']
        ema25_next = price_row_next['ema25'] if 'ema25' in price_row_next else None
        # Entry logic: detect crossover at i, enter at open of next candle (i+1)
        if position is None:
            # Long entry
            if long_dir_prev < short_dir_prev and long_dir > short_dir_prev:
                if open_price_next > ema25_next:
                    entry_price = open_price_next + spread
                    entry_time = time_next
                    direction = 'long'
                    sl_price = entry_price - SL
                    tp_price = entry_price + TP
                    position = {
                        'entry_time': entry_time,
                        'entry_price': entry_price,
                        'direction': direction,
                        'sl_price': sl_price,
                        'tp_price': tp_price,
                        'exit_time': None,
                        'exit_price': None,
                        'pnl': None,
                        'exit_reason': None
                    }
                    entry_idx = i+1
                    i += 1
                    continue
            # Short entry
            if short_dir_prev < long_dir_prev and short_dir > long_dir_prev:
                if open_price_next < ema25_next:
                    entry_price = open_price_next - spread
                    entry_time = time_next
                    direction = 'short'
                    sl_price = entry_price + SL
                    tp_price = entry_price - TP
                    position = {
                        'entry_time': entry_time,
                        'entry_price': entry_price,
                        'direction': direction,
                        'sl_price': sl_price,
                        'tp_price': tp_price,
                        'exit_time': None,
                        'exit_price': None,
                        'pnl': None,
                        'exit_reason': None
                    }
                    entry_idx = i+1
                    i += 1
                    continue
        # Exit logic: check SL/TP/Signal only at close price
        if position is not None:
            while i < len(result_df):
                time_exit = result_df.loc[i, 'time']
                price_row_exit = df.loc[time_exit] if time_exit in df.index else None
                if price_row_exit is None:
                    i += 1
                    continue
                close_price_exit = price_row_exit['close']
                long_dir_exit = result_df.loc[i, 'long_dir']
                short_dir_exit = result_df.loc[i, 'short_dir']
                signal_long = long_dir_exit > short_dir_exit
                signal_short = short_dir_exit > long_dir_exit
                # Signal change exit
                if direction == 'long' and signal_short:
                    exit_price = close_price_exit - spread
                    exit_time = time_exit
                    position['exit_time'] = exit_time
                    position['exit_price'] = exit_price
                    position['pnl'] = (exit_price - position['entry_price']) * lot_size
                    position['exit_reason'] = 'Signal Change (close)'
                    positions.append(position)
                    position = None
                    entry_idx = None
                    entry_price = None
                    entry_time = None
                    direction = None
                    i += 1
                    break
                if direction == 'short' and signal_long:
                    exit_price = close_price_exit + spread
                    exit_time = time_exit
                    position['exit_time'] = exit_time
                    position['exit_price'] = exit_price
                    position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                    position['exit_reason'] = 'Signal Change (close)'
                    positions.append(position)
                    position = None
                    entry_idx = None
                    entry_price = None
                    entry_time = None
                    direction = None
                    i += 1
                    break
                # SL/TP logic at close
                if direction == 'long':
                    if close_price_exit <= position['sl_price']:
                        exit_price = close_price_exit - spread
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'SL (close)'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                    if close_price_exit >= position['tp_price']:
                        exit_price = close_price_exit - spread
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'TP (close)'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                elif direction == 'short':
                    if close_price_exit >= position['sl_price']:
                        exit_price = close_price_exit + spread
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'SL (close)'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                    if close_price_exit <= position['tp_price']:
                        exit_price = close_price_exit + spread
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'TP (close)'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                i += 1
            else:
                # If position is still open at the end, close at last available price
                last_row = result_df.iloc[-1]
                time_exit = last_row['time']
                price_row_exit = df.loc[time_exit] if time_exit in df.index else None
                if price_row_exit is not None:
                    close_price_exit = price_row_exit['close']
                    if direction == 'long':
                        exit_price = close_price_exit - spread
                        position['exit_time'] = time_exit
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'End'
                    elif direction == 'short':
                        exit_price = close_price_exit + spread
                        position['exit_time'] = time_exit
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'End'
                    positions.append(position)
                    position = None
                    entry_idx = None
                    entry_price = None
                    entry_time = None
                    direction = None
            continue
        i += 1
    return positions

if __name__ == "__main__":
    main()
