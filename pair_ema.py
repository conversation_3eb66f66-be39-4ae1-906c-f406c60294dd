

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta > 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")



def main():
    initialize_mt5()
    symbol = 'Volatility 100 (1s) Index'
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, 10000)
    if rates is None or len(rates) == 0:
        print(f"No data returned from MT5 for {symbol}.")
        return
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    # Calculate rolling average of every 5 candles
    df['avg5'] = df['close'].rolling(window=5).mean()
    # Take every 5th value from avg5 to form a new series (set average for each 5-candle group)
    avg5_series = df['avg5'].iloc[4::5].reset_index(drop=True)
    # Calculate EMA(50) on this new series
    ema_on_avg5 = avg5_series.ewm(span=50, adjust=False).mean()
    # For display, align the EMA values back to the original DataFrame (pad with NaN for non-group rows)
    df['Custom_EMA_50'] = np.nan
    df.loc[df.index[4::5], 'Custom_EMA_50'] = ema_on_avg5.values

    print(tabulate(df[['time', 'close', 'Custom_EMA_50']], headers='keys', tablefmt='fancy_grid', showindex=False))

    # Find cross events: previous close below EMA and current close above EMA (cross up), and vice versa (cross down)
    cross_events = []
    for i in range(1, len(df)):
        prev_ema = df['Custom_EMA_50'].iloc[i-1]
        curr_ema = df['Custom_EMA_50'].iloc[i]
        if np.isnan(prev_ema) or np.isnan(curr_ema):
            continue
        prev_close = df['close'].iloc[i-1]
        curr_close = df['close'].iloc[i]
        # Cross up: prev close < prev ema and curr close > curr ema
        if prev_close < prev_ema and curr_close > curr_ema:
            cross_events.append({
                'time': df['time'].iloc[i],
                'close': curr_close,
                'Custom_EMA_50': curr_ema,
                'Cross': 'Up'
            })
        # Cross down: prev close > prev ema and curr close < curr ema
        elif prev_close > prev_ema and curr_close < curr_ema:
            cross_events.append({
                'time': df['time'].iloc[i],
                'close': curr_close,
                'Custom_EMA_50': curr_ema,
                'Cross': 'Down'
            })
    if cross_events:
        print("\nCross Events (close crossing Custom_EMA_50):")
        print(tabulate(cross_events, headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print("\nNo cross events found.")

if __name__ == "__main__":
    main()

