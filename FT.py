
import MetaTrader5 as mt5
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.align import Align
from rich.text import Text
from rich.box import HEAVY
from rich.layout import Layout
from rich import print as rprint
console = Console()
import pandas as pd
from tabulate import tabulate

def init_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def fetch_m3_bars(symbol, days=1):
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=days)
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1 , utc_from, utc_to)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol} in the given period.")
    df = pd.DataFrame(rates)
    if 'time' not in df.columns:
        raise ValueError(f"Returned data does not contain 'time' column. Columns: {df.columns.tolist()}")
    df['time'] = pd.to_datetime(df['time'], unit='s')
    # Remove duplicate timestamps (keep first occurrence)
    dupes = df['time'].duplicated(keep=False)
    if dupes.any():
        print(f"Warning: {dupes.sum()} duplicate timestamps found in price data. Dropping duplicates, keeping first occurrence.")
        df = df[~dupes | ~df.duplicated('time', keep='first')]
    df.set_index('time', inplace=True)
    # Calculate EMA25 on close prices
    df['ema25'] = df['close'].ewm(span=25, adjust=False).mean()
    return df

def calc_long_short_dir(df, n):
    # Modified calculation for long_dir and short_dir
    bullish = (df['close'] > df['open'])
    long_dir_series = pd.Series(0, index=df.index)
    short_dir_series = pd.Series(0, index=df.index)
    long_dir_series[bullish] = (df['close'] - df['open']).abs()[bullish]
    short_dir_series[bullish] = 0
    short_dir_series[~bullish] = (df['close'] - df['open']).abs()[~bullish]
    long_dir_series[~bullish] = 0

    long_dir_sum = long_dir_series.rolling(window=n).sum()
    short_dir_sum = short_dir_series.rolling(window=n).sum()

    result_df = pd.DataFrame({
        'time': df.index,
        'long_dir': long_dir_sum,
        'short_dir': short_dir_sum
    })
    result_df = result_df.iloc[n:]
    return result_df.reset_index(drop=True)

def main():
    init_mt5()
    symbol = "Volatility 100 (1s) Index"
    try:
        n = int(input("Enter period n for calculation (e.g. 5): "))
    except Exception:
        print("Invalid input. Using default n=5.")
        n = 1
    df = fetch_m3_bars(symbol)
    result_df = calc_long_short_dir(df, n)
    positions = backtest_long_short_cross(df, result_df, lot_size=0.7)
    # Prepare table data
    table = []
    # Create a lookup for entry times to row indices for efficiency
    time_to_idx = {row['time']: idx for idx, row in result_df.iterrows()}
    for pos in positions:
        entry_time = pos['entry_time']
        signal_time = None
        # Signal time is the time of the previous candle (when the signal was generated)
        if entry_time is not None and entry_time in time_to_idx:
            idx = time_to_idx[entry_time]
            if idx > 0:
                signal_time = result_df.loc[idx-1, 'time']
            long_dir_val = result_df.loc[idx, 'long_dir']
            short_dir_val = result_df.loc[idx, 'short_dir']
        else:
            long_dir_val = None
            short_dir_val = None
        table.append([
            pos['entry_time'],
            signal_time,
            pos['direction'],
            round(pos['entry_price'], 5),
            long_dir_val,
            short_dir_val,
            pos['exit_time'],
            round(pos['exit_price'], 5) if pos['exit_price'] is not None else None,
            round(pos['pnl'], 2) if pos['pnl'] is not None else None,
            pos.get('exit_reason', '')
        ])
    headers = ["Entry Time", "Signal Time", "Direction", "Entry Price", "Long Dir", "Short Dir", "Exit Time", "Exit Price", "PnL", "Exit Reason"]
    print(tabulate(table, headers=headers, tablefmt="fancy_grid"))

    # Datewise PnL table
    from collections import defaultdict
    date_pnl = defaultdict(float)
    for pos in positions:
        if pos['pnl'] is not None and pos['exit_time'] is not None:
            date = str(pos['exit_time']).split()[0]
            date_pnl[date] += pos['pnl']
    date_table = []
    for date in sorted(date_pnl.keys()):
        date_table.append([date, round(date_pnl[date], 2)])
    print("\nDatewise PnL:")
    print(tabulate(date_table, headers=["Date", "PnL"], tablefmt="fancy_grid"))

    # Profitable and loss positions
    profitable = sum(1 for pos in positions if pos['pnl'] is not None and pos['pnl'] > 0)
    loss = sum(1 for pos in positions if pos['pnl'] is not None and pos['pnl'] < 0)
    print(f"\nNumber of Profitable Positions: {profitable}")
    print(f"Number of Loss Positions: {loss}")

    # Net PnL
    net_pnl = sum(pos['pnl'] for pos in positions if pos['pnl'] is not None)
    print(f"\nNet PnL: {round(net_pnl, 2)}")

    # Max Drawdown calculation
    equity_curve = []
    equity = 0
    for pos in positions:
        if pos['pnl'] is not None:
            equity += pos['pnl']
            equity_curve.append(equity)
    max_drawdown = 0
    peak = 0
    for eq in equity_curve:
        if eq > peak:
            peak = eq
        dd = peak - eq
        if dd > max_drawdown:
            max_drawdown = dd
    print(f"Max Drawdown: {round(max_drawdown, 2)}")
    mt5.shutdown()
def backtest_long_short_cross(df, result_df, lot_size=0.7):
    positions = []
    position = None
    entry_idx = None
    entry_price = None
    entry_time = None
    direction = None
    SL = 5.5
    TP = 1.5
    i = 1
    while i < len(result_df):
        long_dir_prev = result_df.loc[i-1, 'long_dir']
        short_dir_prev = result_df.loc[i-1, 'short_dir']
        long_dir = result_df.loc[i, 'long_dir']
        short_dir = result_df.loc[i, 'short_dir']
        time_prev = result_df.loc[i-1, 'time']
        time_curr = result_df.loc[i, 'time']
        price_row_prev = df.loc[time_prev] if time_prev in df.index else None
        price_row_curr = df.loc[time_curr] if time_curr in df.index else None
        if price_row_prev is None or price_row_curr is None:
            i += 1
            continue
        open_price_next = price_row_curr['open']
        # Entry logic: use previous candle's signal for entry, enter at open of next candle
        if position is None:
            if long_dir_prev < short_dir_prev and long_dir > short_dir:
                entry_price = open_price_next
                entry_time = time_curr
                direction = 'long'
                sl_price = entry_price - SL
                tp_price = entry_price + TP
                position = {
                    'entry_time': entry_time,
                    'entry_price': entry_price,
                    'direction': direction,
                    'sl_price': sl_price,
                    'tp_price': tp_price,
                    'exit_time': None,
                    'exit_price': None,
                    'pnl': None,
                    'exit_reason': None
                }
                entry_idx = i
                i += 1
                continue
            elif short_dir_prev < long_dir_prev and short_dir > long_dir:
                entry_price = open_price_next
                entry_time = time_curr
                direction = 'short'
                sl_price = entry_price + SL
                tp_price = entry_price - TP
                position = {
                    'entry_time': entry_time,
                    'entry_price': entry_price,
                    'direction': direction,
                    'sl_price': sl_price,
                    'tp_price': tp_price,
                    'exit_time': None,
                    'exit_price': None,
                    'pnl': None,
                    'exit_reason': None
                }
                entry_idx = i
                i += 1
                continue
        # Exit logic: check SL/TP/Signal only at the open of the next candle (no lookahead)
        if position is not None:
            while i < len(result_df):
                time_exit = result_df.loc[i, 'time']
                price_row_exit = df.loc[time_exit] if time_exit in df.index else None
                if price_row_exit is None:
                    i += 1
                    continue
                open_price_exit = price_row_exit['open']
                long_dir_exit = result_df.loc[i, 'long_dir']
                short_dir_exit = result_df.loc[i, 'short_dir']
                # SL/TP logic (checked only at open)
                if direction == 'long':
                    if open_price_exit <= sl_price:
                        exit_price = open_price_exit
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'SL'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                    if open_price_exit >= tp_price:
                        exit_price = open_price_exit
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'TP'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                    if short_dir_exit > long_dir_exit:
                        exit_price = open_price_exit
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'Signal'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                elif direction == 'short':
                    if open_price_exit >= sl_price:
                        exit_price = open_price_exit
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'SL'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                    if open_price_exit <= tp_price:
                        exit_price = open_price_exit
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'TP'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                    if long_dir_exit > short_dir_exit:
                        exit_price = open_price_exit
                        exit_time = time_exit
                        position['exit_time'] = exit_time
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'Signal'
                        positions.append(position)
                        position = None
                        entry_idx = None
                        entry_price = None
                        entry_time = None
                        direction = None
                        i += 1
                        break
                i += 1
            else:
                # If position is still open at the end, close at last available price
                last_row = result_df.iloc[-1]
                time_exit = last_row['time']
                price_row_exit = df.loc[time_exit] if time_exit in df.index else None
                if price_row_exit is not None:
                    open_price_exit = price_row_exit['open']
                    if direction == 'long':
                        exit_price = open_price_exit
                        position['exit_time'] = time_exit
                        position['exit_price'] = exit_price
                        position['pnl'] = (exit_price - position['entry_price']) * lot_size
                        position['exit_reason'] = 'End'
                    elif direction == 'short':
                        exit_price = open_price_exit
                        position['exit_time'] = time_exit
                        position['exit_price'] = exit_price
                        position['pnl'] = (position['entry_price'] - exit_price) * lot_size
                        position['exit_reason'] = 'End'
                    positions.append(position)
                    position = None
                    entry_idx = None
                    entry_price = None
                    entry_time = None
                    direction = None
            continue
        i += 1
    return positions

import time

def run_live_trading(symbol, n=1, lot_size=0.7, SL=5.5, TP=1.5, poll_interval=0.05):
    init_mt5()
    # Remove dashboard and history logic for immediate entry/exit
    import time
    from datetime import datetime, timedelta
    poll_interval = 0.01  # seconds (reduce interval for faster execution)
    timeframe_minutes = None
    last_closed_candle = None
    prev_closed_candle = None
    position = None
    open_ticket = None
    last_processed_time = None
    while True:
        try:
            df = fetch_m3_bars(symbol, days=2)
            result_df = calc_long_short_dir(df, n)
            if len(result_df) < 2:
                time.sleep(poll_interval)
                continue
            closed_idx = len(result_df) - 2
            latest_closed_row = result_df.loc[closed_idx]
            prev_closed_row = result_df.loc[closed_idx - 1]
            # Only process if new closed candle
            if last_processed_time == latest_closed_row['time']:
                time.sleep(poll_interval)
                continue
            last_processed_time = latest_closed_row['time']
            long_dir_prev = prev_closed_row['long_dir']
            short_dir_prev = prev_closed_row['short_dir']
            long_dir_curr = latest_closed_row['long_dir']
            short_dir_curr = latest_closed_row['short_dir']
            # Entry logic: check for cross on latest closed candle
            if position is None:
                ema25_curr = None
                if latest_closed_row['time'] in df.index:
                    ema25_curr = df.loc[latest_closed_row['time'], 'ema25']
                # Entry ONLY at open price of next candle
                open_price_next = df.loc[result_df.loc[closed_idx+1]['time'], 'open'] if closed_idx+1 < len(result_df) else None
                if open_price_next is not None:
                    if long_dir_prev < short_dir_prev and long_dir_curr > short_dir_curr:
                        if ema25_curr is not None and open_price_next > ema25_curr:
                            entry_time = datetime.now()
                            entry_price = open_price_next
                            direction = 'long'
                            sl_price = entry_price - SL
                            tp_price = entry_price + TP
                            request = {
                                "action": mt5.TRADE_ACTION_DEAL,
                                "symbol": symbol,
                                "volume": lot_size,
                                "type": mt5.ORDER_TYPE_BUY,
                                "price": entry_price,
                                "deviation": 10,
                                "magic": 123456,
                                "comment": "Live long entry",
                                "type_time": mt5.ORDER_TIME_GTC,
                                "type_filling": mt5.ORDER_FILLING_FOK
                            }
                            result = mt5.order_send(request)
                            if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
                                open_ticket = result.order
                                position = {
                                    'entry_time': entry_time,
                                    'entry_price': entry_price,
                                    'direction': direction,
                                    'sl_price': sl_price,
                                    'tp_price': tp_price,
                                    'exit_time': None,
                                    'exit_price': None,
                                    'pnl': None,
                                    'exit_reason': None,
                                    'ticket': open_ticket
                                }
                    elif short_dir_prev < long_dir_prev and short_dir_curr > long_dir_curr:
                        if ema25_curr is not None and open_price_next < ema25_curr:
                            entry_time = datetime.now()
                            entry_price = open_price_next
                            direction = 'short'
                            sl_price = entry_price + SL
                            tp_price = entry_price - TP
                            request = {
                                "action": mt5.TRADE_ACTION_DEAL,
                                "symbol": symbol,
                                "volume": lot_size,
                                "type": mt5.ORDER_TYPE_SELL,
                                "price": entry_price,
                                "deviation": 10,
                                "magic": 123456,
                                "comment": "Live short entry",
                                "type_time": mt5.ORDER_TIME_GTC,
                                "type_filling": mt5.ORDER_FILLING_FOK
                            }
                            result = mt5.order_send(request)
                            if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
                                open_ticket = result.order
                                position = {
                                    'entry_time': entry_time,
                                    'entry_price': entry_price,
                                    'direction': direction,
                                    'sl_price': sl_price,
                                    'tp_price': tp_price,
                                    'exit_time': None,
                                    'exit_price': None,
                                    'pnl': None,
                                    'exit_reason': None,
                                    'ticket': open_ticket
                                }
            # Exit logic: check SL/TP/Signal ONLY at open and close price of the candle
            if position is not None and open_ticket is not None:
                open_price_closed = latest_closed_row['open']
                close_price_closed = latest_closed_row['close']
                exit_time_open = datetime.now()
                exit_time_close = datetime.now()
                exited = False
                if position['direction'] == 'long':
                    if open_price_closed <= position['sl_price']:
                        exit_price = open_price_closed
                        close_type = mt5.ORDER_TYPE_SELL
                        exit_reason = 'SL (open)'
                        exited = True
                    elif open_price_closed >= position['tp_price']:
                        exit_price = open_price_closed
                        close_type = mt5.ORDER_TYPE_SELL
                        exit_reason = 'TP (open)'
                        exited = True
                    elif short_dir_curr > long_dir_curr:
                        exit_price = open_price_closed
                        close_type = mt5.ORDER_TYPE_SELL
                        exit_reason = 'Signal (open)'
                        exited = True
                    if exited:
                        close_request = {
                            "action": mt5.TRADE_ACTION_DEAL,
                            "symbol": symbol,
                            "volume": lot_size,
                            "type": close_type,
                            "position": open_ticket,
                            "price": exit_price,
                            "deviation": 10,
                            "magic": 123456,
                            "comment": f"Live long exit {exit_reason}",
                            "type_time": mt5.ORDER_TIME_GTC,
                            "type_filling": mt5.ORDER_FILLING_FOK
                        }
                        result = mt5.order_send(close_request)
                        if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
                            position['exit_time'] = exit_time_open
                            position['exit_price'] = exit_price
                            position['exit_reason'] = exit_reason
                        position = None
                        open_ticket = None
                        continue
                    # If not exited at open, check close
                    exited = False
                    if position is not None:
                        if close_price_closed <= position['sl_price']:
                            exit_price = close_price_closed
                            close_type = mt5.ORDER_TYPE_SELL
                            exit_reason = 'SL (close)'
                            exited = True
                        elif close_price_closed >= position['tp_price']:
                            exit_price = close_price_closed
                            close_type = mt5.ORDER_TYPE_SELL
                            exit_reason = 'TP (close)'
                            exited = True
                        elif short_dir_curr > long_dir_curr:
                            exit_price = close_price_closed
                            close_type = mt5.ORDER_TYPE_SELL
                            exit_reason = 'Signal (close)'
                            exited = True
                        if exited:
                            close_request = {
                                "action": mt5.TRADE_ACTION_DEAL,
                                "symbol": symbol,
                                "volume": lot_size,
                                "type": close_type,
                                "position": open_ticket,
                                "price": exit_price,
                                "deviation": 10,
                                "magic": 123456,
                                "comment": f"Live long exit {exit_reason}",
                                "type_time": mt5.ORDER_TIME_GTC,
                                "type_filling": mt5.ORDER_FILLING_FOK
                            }
                            result = mt5.order_send(close_request)
                            if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
                                position['exit_time'] = exit_time_close
                                position['exit_price'] = exit_price
                                position['exit_reason'] = exit_reason
                            position = None
                            open_ticket = None
                            continue
                elif position['direction'] == 'short':
                    if open_price_closed >= position['sl_price']:
                        exit_price = open_price_closed
                        close_type = mt5.ORDER_TYPE_BUY
                        exit_reason = 'SL (open)'
                        exited = True
                    elif open_price_closed <= position['tp_price']:
                        exit_price = open_price_closed
                        close_type = mt5.ORDER_TYPE_BUY
                        exit_reason = 'TP (open)'
                        exited = True
                    elif long_dir_curr > short_dir_curr:
                        exit_price = open_price_closed
                        close_type = mt5.ORDER_TYPE_BUY
                        exit_reason = 'Signal (open)'
                        exited = True
                    if exited:
                        close_request = {
                            "action": mt5.TRADE_ACTION_DEAL,
                            "symbol": symbol,
                            "volume": lot_size,
                            "type": close_type,
                            "position": open_ticket,
                            "price": exit_price,
                            "deviation": 10,
                            "magic": 123456,
                            "comment": f"Live short exit {exit_reason}",
                            "type_time": mt5.ORDER_TIME_GTC,
                            "type_filling": mt5.ORDER_FILLING_FOK
                        }
                        result = mt5.order_send(close_request)
                        if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
                            position['exit_time'] = exit_time_open
                            position['exit_price'] = exit_price
                            position['exit_reason'] = exit_reason
                        position = None
                        open_ticket = None
                        continue
                    # If not exited at open, check close
                    exited = False
                    if position is not None:
                        if close_price_closed >= position['sl_price']:
                            exit_price = close_price_closed
                            close_type = mt5.ORDER_TYPE_BUY
                            exit_reason = 'SL (close)'
                            exited = True
                        elif close_price_closed <= position['tp_price']:
                            exit_price = close_price_closed
                            close_type = mt5.ORDER_TYPE_BUY
                            exit_reason = 'TP (close)'
                            exited = True
                        elif long_dir_curr > short_dir_curr:
                            exit_price = close_price_closed
                            close_type = mt5.ORDER_TYPE_BUY
                            exit_reason = 'Signal (close)'
                            exited = True
                        if exited:
                            close_request = {
                                "action": mt5.TRADE_ACTION_DEAL,
                                "symbol": symbol,
                                "volume": lot_size,
                                "type": close_type,
                                "position": open_ticket,
                                "price": exit_price,
                                "deviation": 10,
                                "magic": 123456,
                                "comment": f"Live short exit {exit_reason}",
                                "type_time": mt5.ORDER_TIME_GTC,
                                "type_filling": mt5.ORDER_FILLING_FOK
                            }
                            result = mt5.order_send(close_request)
                            if result is not None and result.retcode == mt5.TRADE_RETCODE_DONE:
                                position['exit_time'] = exit_time_close
                                position['exit_price'] = exit_price
                                position['exit_reason'] = exit_reason
                            position = None
                            open_ticket = None
                            continue
        except Exception as e:
            time.sleep(poll_interval)

if __name__ == "__main__":
    # For live trading, comment out main() and use run_live_trading
    # main()
    run_live_trading(symbol="Volatility 100 (1s) Index", n=1, lot_size=0.7, SL=1.5, TP=3.5, poll_interval=0.05)
