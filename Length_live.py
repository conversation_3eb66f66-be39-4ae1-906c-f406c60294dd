import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

class TradePosition:
    def __init__(self, symbol, entry_index, entry_timestamp, entry_trade_price, trade_direction, entry_rsi_value, entry_reason):
        self.symbol = symbol
        self.entry_index = entry_index
        self.entry_timestamp = entry_timestamp
        self.entry_trade_price = entry_trade_price
        self.trade_direction = trade_direction  # 'long' or 'short'
        self.entry_rsi_value = entry_rsi_value
        self.exit_index = None
        self.exit_timestamp = None
        self.exit_trade_price = None
        self.exit_rsi_value = None
        self.trade_pnl = None
        self.entry_reason = entry_reason

    def close(self, exit_index, exit_timestamp, exit_trade_price, exit_rsi_value, trade_pnl, close_reason):
        self.exit_index = exit_index
        self.exit_timestamp = exit_timestamp
        self.exit_trade_price = exit_trade_price
        self.exit_rsi_value = exit_rsi_value
        self.trade_pnl = trade_pnl
        self.close_reason = close_reason

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta < 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")


def main():
    initialize_mt5()
    symbols = [
        "Volatility 30 (1s) Index",
        "Volatility 10 (1s) Index",
        "Volatility 100 (1s) Index",
        "Volatility 75 (1s) Index"
    ]
    from datetime import datetime, timedelta
    utc_now = datetime.now()
    utc_start = utc_now - timedelta(days=30)

    # Prepare data for all symbols
    symbol_data = {}
    for symbol in symbols:
        lower_tf_rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, utc_start, utc_now)
        higher_tf_rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M5, utc_start, utc_now)
        if lower_tf_rates is None or len(lower_tf_rates) == 0 or higher_tf_rates is None or len(higher_tf_rates) == 0:
            print(f"No data returned from MT5 for {symbol}.")
            mt5.shutdown()
            return
        df_lower = pd.DataFrame(lower_tf_rates)
        df_lower['time'] = pd.to_datetime(df_lower['time'], unit='s')
        df_higher = pd.DataFrame(higher_tf_rates)
        df_higher['time'] = pd.to_datetime(df_higher['time'], unit='s')
        # Remove the last (possibly incomplete) M5 candle so only fully closed candles are used
        df_higher = df_higher.iloc[:-1] if len(df_higher) > 0 else df_higher
        df_lower['RSI_lower'] = calculate_rsi(df_lower['close'], rsi_period=5)
        df_higher['RSI_higher'] = calculate_rsi(df_higher['close'], rsi_period=10)
        # Shift RSI_higher by 1 to use previous M5 candle's RSI for each M1 bar
        df_higher['RSI_higher_prev'] = df_higher['RSI_higher'].shift(1)
        # Add M5_time to df_higher for alignment check
        df_higher['M5_time'] = df_higher['time']
        # Align each M1 bar with the previous closed M5 candle's RSI value
        df_lower = pd.merge_asof(
            df_lower.sort_values('time'),
            df_higher[['time', 'RSI_higher_prev', 'M5_time']].sort_values('time'),
            on='time',
            direction='backward'
        )
        # Rename for consistency in rest of code
        df_lower['RSI_higher'] = df_lower['RSI_higher_prev']
        df_lower['EMA50_lower'] = df_lower['close'].ewm(span=25, adjust=False).mean()
        symbol_data[symbol] = df_lower

    lot_size_sequence = [0.7, 1.2, 1.5]
    lot_size_index = 0
    current_lot_size = lot_size_sequence[lot_size_index]
    trade_spread = 0.21
    take_profit = 3.5
    stop_loss = 3.5
    executed_positions = []
    active_position = None

    nan_skipped_bars = 0
    rsi_cross_above_30 = 0
    rsi_cross_below_70 = 0
    missed_long_signals = 0
    missed_short_signals = 0
    rsi_printed_count = 0

    # Prepare iterators for each symbol
    symbol_iters = {symbol: iter(range(1, len(symbol_data[symbol]))) for symbol in symbols}
    symbol_indices = {symbol: 1 for symbol in symbols}
    # Track which symbol is currently being processed for a position
    # Merge all symbol data into a single time-ordered list of bars
    merged_bars = []
    for symbol in symbols:
        df_lower = symbol_data[symbol]
        for i in range(1, len(df_lower)):
            curr_bar = df_lower.iloc[i]
            prev_bar = df_lower.iloc[i-1]
            merged_bars.append({
                'symbol': symbol,
                'index': i,
                'curr_bar': curr_bar,
                'prev_bar': prev_bar
            })
    # Sort merged bars by time
    merged_bars.sort(key=lambda x: x['curr_bar']['time'])
    # Print sample alignment between M1 and M5 bars for first symbol
    print("Sample alignment between M1 and M5 bars (first 20 rows):")
    sample_df = symbol_data[symbols[0]][['time', 'M5_time', 'RSI_lower', 'RSI_higher']].head(20)
    print(sample_df)

    bar_idx = 0
    while bar_idx < len(merged_bars):
        if active_position is None:
            entry_found = False
            while bar_idx < len(merged_bars):
                bar = merged_bars[bar_idx]
                symbol = bar['symbol']
                i = bar['index']
                curr_bar = bar['curr_bar']
                prev_bar = bar['prev_bar']
                lower_close_price = curr_bar['close']
                lower_time = curr_bar['time']
                prev_rsi_lower = prev_bar['RSI_lower']
                curr_rsi_lower = curr_bar['RSI_lower']
                curr_rsi_higher = curr_bar['RSI_higher']
                curr_ema50_lower = curr_bar['EMA50_lower']
                # Skip bars with NaN RSI or EMA values
                if pd.isna(prev_rsi_lower) or pd.isna(curr_rsi_lower) or pd.isna(curr_rsi_higher) or pd.isna(curr_ema50_lower):
                    nan_skipped_bars += 1
                    bar_idx += 1
                    continue
                if rsi_printed_count < 10:
                    print(f"Bar {i} [{symbol}]: time={lower_time}, close={lower_close_price:.2f}, RSI_lower={curr_rsi_lower:.2f}, RSI_higher={curr_rsi_higher:.2f}, EMA50_lower={curr_ema50_lower:.2f}")
                    rsi_printed_count += 1
                # Count lower timeframe RSI crosses regardless of HTF filter
                # curr_rsi_higher is already the previous M5 candle's RSI due to the shift in merge
                if prev_rsi_lower < 30 and curr_rsi_lower >= 30:
                    rsi_cross_above_30 += 1
                    if curr_rsi_higher <= 50:
                        missed_long_signals += 1
                if prev_rsi_lower > 70 and curr_rsi_lower <= 70:
                    rsi_cross_below_70 += 1
                    if curr_rsi_higher >= 50:
                        missed_short_signals += 1
                # Entry logic uses previous M5 candle's RSI (curr_rsi_higher)
                is_long_entry = (prev_rsi_lower < 30 and curr_rsi_lower >= 30 and curr_rsi_higher > 55 and lower_close_price < curr_ema50_lower)
                is_short_entry = (prev_rsi_lower > 70 and curr_rsi_lower <= 70 and curr_rsi_higher < 45 and lower_close_price > curr_ema50_lower)
                if is_long_entry or is_short_entry:
                    # Entry on next candle's open
                    next_idx = i + 1
                    df_lower_signal = symbol_data[symbol]
                    if next_idx < len(df_lower_signal):
                        next_bar = df_lower_signal.iloc[next_idx]
                        entry_time = next_bar['time']
                        entry_open = next_bar['open']
                        entry_rsi = next_bar['RSI_lower']
                        if is_long_entry:
                            entry_trade_price = entry_open + trade_spread
                            active_position = TradePosition(symbol, next_idx, entry_time, entry_trade_price, 'long', entry_rsi, 'Long entry (RSI cross + HTF bearish, next open)')
                        else:
                            entry_trade_price = entry_open - trade_spread
                            active_position = TradePosition(symbol, next_idx, entry_time, entry_trade_price, 'short', entry_rsi, 'Short entry (RSI cross + HTF bullish, next open)')
                        bar_idx += 1
                        entry_found = True
                        break
                    else:
                        # No next candle, cannot enter
                        bar_idx += 1
                        continue
                bar_idx += 1
            if not entry_found:
                break  # No more signals
        if active_position is not None:
            symbol = active_position.symbol
            df_lower = symbol_data[symbol]
            i = active_position.entry_index + 1
            while i < len(df_lower):
                curr_bar = df_lower.iloc[i]
                lower_close_price = curr_bar['close']
                curr_ema50_lower = curr_bar['EMA50_lower']
                curr_rsi_lower = curr_bar['RSI_lower']
                curr_rsi_higher = curr_bar['RSI_higher']
                trade_direction = active_position.trade_direction
                entry_trade_price = active_position.entry_trade_price
                exit_trade_price = lower_close_price
                if trade_direction == 'short':
                    sl_hit = lower_close_price - entry_trade_price <= -stop_loss
                    tp_hit = lower_close_price - entry_trade_price >= take_profit
                    if sl_hit:
                        exit_trade_price -= trade_spread
                        trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'SL -{stop_loss}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        break
                    elif tp_hit:
                        exit_trade_price -= trade_spread
                        trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'TP +{take_profit}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        break
                elif trade_direction == 'long':
                    sl_hit = entry_trade_price - lower_close_price <= -stop_loss
                    tp_hit = entry_trade_price - lower_close_price >= take_profit
                    if sl_hit:
                        exit_trade_price += trade_spread
                        trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'SL +{stop_loss}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        break
                    elif tp_hit:
                        exit_trade_price += trade_spread
                        trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'TP -{take_profit}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        break
                i += 1
            # If position is still open at the end of symbol data, close it
            if active_position:
                curr_bar = df_lower.iloc[-1]
                lower_close_price = curr_bar['close']
                curr_rsi_lower = curr_bar['RSI_lower']
                if trade_direction == 'long':
                    exit_trade_price = lower_close_price - trade_spread
                    trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                    active_position.close(len(df_lower)-1, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, 'End of data')
                else:
                    exit_trade_price = lower_close_price + trade_spread
                    trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                    active_position.close(len(df_lower)-1, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, 'End of data')
                executed_positions.append(active_position)
                if trade_pnl > 0:
                    if lot_size_index < len(lot_size_sequence) - 1:
                        lot_size_index += 1
                elif trade_pnl < 0:
                    lot_size_index = 0
                current_lot_size = lot_size_sequence[lot_size_index]
                active_position = None
        else:
            # Process exit for the active position's symbol only
            symbol = active_position.symbol
            df_lower = symbol_data[symbol]
            i = symbol_indices[symbol]
            while i < len(df_lower):
                curr_bar = df_lower.iloc[i]
                lower_close_price = curr_bar['close']
                curr_ema50_lower = curr_bar['EMA50_lower']
                curr_rsi_lower = curr_bar['RSI_lower']
                curr_rsi_higher = curr_bar['RSI_higher']
                trade_direction = active_position.trade_direction
                entry_trade_price = active_position.entry_trade_price
                # Exit on TP/SL only
                exit_trade_price = lower_close_price
                if trade_direction == 'short':
                    if lower_close_price - entry_trade_price >= take_profit:
                        exit_trade_price -= trade_spread
                        trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'TP +{take_profit}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        symbol_indices[symbol] = i + 1
                        break
                    elif lower_close_price - entry_trade_price <= -stop_loss:
                        exit_trade_price -= trade_spread
                        trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'SL -{stop_loss}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        symbol_indices[symbol] = i + 1
                        break
                elif trade_direction == 'long':
                    if entry_trade_price - lower_close_price >= take_profit:
                        exit_trade_price += trade_spread
                        trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'TP -{take_profit}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        symbol_indices[symbol] = i + 1
                        break
                    elif entry_trade_price - lower_close_price <= -stop_loss:
                        exit_trade_price += trade_spread
                        trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                        active_position.close(i, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, f'SL +{stop_loss}')
                        executed_positions.append(active_position)
                        if trade_pnl > 0:
                            if lot_size_index < len(lot_size_sequence) - 1:
                                lot_size_index += 1
                        elif trade_pnl < 0:
                            lot_size_index = 0
                        current_lot_size = lot_size_sequence[lot_size_index]
                        active_position = None
                        symbol_indices[symbol] = i + 1
                        break
                i += 1
            # If position is still open at the end of symbol data, close it
            if active_position:
                curr_bar = df_lower.iloc[-1]
                lower_close_price = curr_bar['close']
                curr_rsi_lower = curr_bar['RSI_lower']
                if trade_direction == 'long':
                    exit_trade_price = lower_close_price - trade_spread
                    trade_pnl = (exit_trade_price - entry_trade_price) * current_lot_size
                    active_position.close(len(df_lower)-1, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, 'End of data')
                else:
                    exit_trade_price = lower_close_price + trade_spread
                    trade_pnl = (entry_trade_price - exit_trade_price) * current_lot_size
                    active_position.close(len(df_lower)-1, curr_bar['time'], exit_trade_price, curr_rsi_lower, trade_pnl, 'End of data')
                executed_positions.append(active_position)
                if trade_pnl > 0:
                    if lot_size_index < len(lot_size_sequence) - 1:
                        lot_size_index += 1
                elif trade_pnl < 0:
                    lot_size_index = 0
                current_lot_size = lot_size_sequence[lot_size_index]
                active_position = None
                symbol_indices[symbol] = len(df_lower)


    print(f"Bars skipped due to NaN RSI/EMA: {nan_skipped_bars}")
    print(f"Lower timeframe RSI crossed above 30: {rsi_cross_above_30} times")
    print(f"Lower timeframe RSI crossed below 70: {rsi_cross_below_70} times")
    print(f"Long signals missed due to higher timeframe filter: {missed_long_signals}")
    print(f"Short signals missed due to higher timeframe filter: {missed_short_signals}")

    # All positions are closed in the main loop above

    # Prepare results table
    if executed_positions:
        results = []
        net_pnl = 0.0
        profitable_count = 0
        loss_count = 0
        # For datewise stats
        date_stats = {}
        for pos in executed_positions:
            entry_date = pos.entry_timestamp.date() if hasattr(pos.entry_timestamp, 'date') else pd.to_datetime(pos.entry_timestamp).date()
            pnl = pos.trade_pnl if pos.trade_pnl is not None else 0.0
            is_profit = pnl > 0
            is_loss = pnl < 0
            if entry_date not in date_stats:
                date_stats[entry_date] = {'pnl': 0.0, 'profit_trades': 0, 'loss_trades': 0}
            date_stats[entry_date]['pnl'] += pnl
            if is_profit:
                date_stats[entry_date]['profit_trades'] += 1
            elif is_loss:
                date_stats[entry_date]['loss_trades'] += 1

            results.append({
                'Symbol': pos.symbol,
                'Entry Time': pos.entry_timestamp,
                'Entry Price': round(pos.entry_trade_price, 2),
                'Entry RSI': round(pos.entry_rsi_value, 2),
                'Direction': pos.trade_direction,
                'Exit Time': pos.exit_timestamp,
                'Exit Price': round(pos.exit_trade_price, 2) if pos.exit_trade_price is not None else None,
                'Exit RSI': round(pos.exit_rsi_value, 2) if pos.exit_rsi_value is not None else None,
                'PnL': round(pos.trade_pnl, 2) if pos.trade_pnl is not None else None,
                'Reason': getattr(pos, 'close_reason', None)
            })
            if pos.trade_pnl is not None:
                net_pnl += pos.trade_pnl
                if pos.trade_pnl > 0:
                    profitable_count += 1
                elif pos.trade_pnl < 0:
                    loss_count += 1
        print(tabulate(results, headers='keys', tablefmt='psql', showindex=False))
        print(f"\nNet PnL: {round(net_pnl, 2)}")
        print(f"Profitable positions: {profitable_count}")
        print(f"Loss positions: {loss_count}")

        # Print datewise PnL table
        datewise_table = []
        for date, stats in sorted(date_stats.items()):
            datewise_table.append({
                'Date': date,
                'Total PnL': round(stats['pnl'], 2),
                'Profitable Trades': stats['profit_trades'],
                'Loss Trades': stats['loss_trades']
            })
        print("\nDatewise PnL Table:")
        print(tabulate(datewise_table, headers='keys', tablefmt='psql', showindex=False))

        # Calculate max drawdown
        equity_curve = []
        cumulative = 0.0
        for pos in executed_positions:
            if pos.trade_pnl is not None:
                cumulative += pos.trade_pnl
            equity_curve.append(cumulative)
        max_drawdown = 0.0
        peak = float('-inf')
        for eq in equity_curve:
            if eq > peak:
                peak = eq
            drawdown = peak - eq
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        print(f"Max Drawdown: {round(max_drawdown, 2)}")
    else:
        print('No trades executed.')
    mt5.shutdown()

if __name__ == "__main__":
    main()
