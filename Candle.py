

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta > 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")



def main():
    initialize_mt5()
    symbol = 'Volatility 100 (1s) Index'
    df = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M3, 0, 10000)
    df = pd.DataFrame(df)
    df['time'] = pd.to_datetime(df['time'], unit='s')

    def is_strong_candle(row, wick_threshold=0.4):
        body = abs(row['close'] - row['open'])
        upper_wick = row['high'] - max(row['close'], row['open'])
        lower_wick = min(row['close'], row['open']) - row['low']
        total_wick = upper_wick + lower_wick
        # Candle is strong if wick is less than threshold * body
        return body > 0 and total_wick < wick_threshold * body

    def similar_size(row1, row2, size_diff_threshold=0.4):
        body1 = abs(row1['close'] - row1['open'])
        body2 = abs(row2['close'] - row2['open'])
        if body1 == 0 or body2 == 0:
            return False
        diff = abs(body1 - body2) / max(body1, body2)
        return diff <= size_diff_threshold


    # Calculate EMA 50
    df['ema50'] = df['close'].ewm(span=15, adjust=False).mean()

    results = []
    pair_indices = []
    for i in range(len(df) - 1):
        row1 = df.iloc[i]
        row2 = df.iloc[i+1]
        if is_strong_candle(row1) and is_strong_candle(row2):
            if not similar_size(row1, row2):
                continue
            dir1 = row1['close'] > row1['open']
            dir2 = row2['close'] > row2['open']
            if dir1 != dir2:
                highest_high = max(row1['high'], row2['high'])
                lowest_low = min(row1['low'], row2['low'])
                results.append({
                    'Time': row2['time'],
                    'Highest High': highest_high,
                    'Lowest Low': lowest_low
                })
                pair_indices.append(i+1)  # index of the second candle in the pair

    if results:
        print("Pair Equal Candle Formations:")
        print(tabulate(results, headers='keys', tablefmt='pretty'))
    else:
        print("No pair equal candle formations found.")

    # --- Backtesting Logic ---\
    lot_size = 0.7
    spread = 0.22
    tp_points = 3.7
    trades = []
    max_sl_dist = 4.2
    idx = 0
    last_exit_idx = -1  # Track last exit candle index to avoid overlapping positions
    while idx < len(pair_indices):
        pair_idx = pair_indices[idx]
        highest_high = results[idx]['Highest High']
        lowest_low = results[idx]['Lowest Low']
        for j in range(max(pair_idx+1, last_exit_idx+1), len(df)):
            candle = df.iloc[j]
            entry_time = candle['time']
            ema50 = candle['ema50']
            long_signal = candle['close'] > highest_high and ema50 < candle['close']
            short_signal = candle['close'] < lowest_low and ema50 > candle['close']
            if long_signal or short_signal:
                direction = 'Long' if long_signal else 'Short'
                entry = candle['close'] + spread if long_signal else candle['close'] - spread
                # Set SL and TP for initial direction
                if long_signal:
                    sl_candidate = lowest_low
                    sl = max(entry - max_sl_dist, sl_candidate)
                    if entry - sl > max_sl_dist:
                        sl = entry - max_sl_dist
                    tp = entry + tp_points
                else:
                    sl_candidate = highest_high
                    sl = max(entry + 0.01, min(entry + max_sl_dist, sl_candidate))
                    # Ensure SL is always above entry for short
                    if sl - entry > max_sl_dist:
                        sl = entry + max_sl_dist
                    tp = entry - tp_points

                exit_time = None
                exit_price = None
                pnl = None
                exit_idx = None
                sl_hit = False
                for k in range(j+1, len(df)):
                    next_candle = df.iloc[k]
                    low = next_candle['low']
                    high = next_candle['high']
                    if long_signal:
                        if low <= sl and high >= tp:
                            exit_price = sl - spread
                            exit_time = next_candle['time']
                            exit_idx = k
                            sl_hit = True
                            break
                        elif high >= tp:
                            exit_price = tp - spread
                            exit_time = next_candle['time']
                            exit_idx = k
                            break
                        elif low <= sl:
                            exit_price = sl - spread
                            exit_time = next_candle['time']
                            exit_idx = k
                            sl_hit = True
                            break
                    else:
                        if low <= tp and high >= sl:
                            # Both hit in same candle, assume SL first (worst case)
                            exit_price = sl + spread
                            exit_time = next_candle['time']
                            exit_idx = k
                            sl_hit = True
                            break
                        elif high >= sl:
                            exit_price = sl + spread
                            exit_time = next_candle['time']
                            exit_idx = k
                            sl_hit = True
                            break
                        elif low <= tp:
                            exit_price = tp + spread
                            exit_time = next_candle['time']
                            exit_idx = k
                            break
                if exit_price is None:
                    if long_signal:
                        exit_price = df.iloc[-1]['close'] - spread
                    else:
                        exit_price = df.iloc[-1]['close'] + spread
                    exit_time = df.iloc[-1]['time']
                    exit_idx = len(df) - 1
                # Calculate PnL correctly for both long and short
                if direction == 'Long':
                    pnl = (exit_price - entry) * lot_size
                else:
                    pnl = (entry - exit_price) * lot_size
                trades.append({
                    'Type': direction,
                    'Entry Time': entry_time,
                    'Entry Price': entry,
                    'SL': sl,
                    'TP': (entry + tp_points) if direction == 'Long' else (entry - tp_points),
                    'Exit Time': exit_time,
                    'Exit Price': exit_price,
                    'PnL': pnl
                })
                # If SL hit, take reverse position immediately
                if sl_hit:
                    reverse_direction = 'Short' if direction == 'Long' else 'Long'
                    reverse_entry = exit_price  # Enter at SL exit price
                    # Set SL and TP for reverse
                    if reverse_direction == 'Long':
                        sl_candidate = lowest_low
                        reverse_sl = max(reverse_entry - max_sl_dist, sl_candidate)
                        if reverse_entry - reverse_sl > max_sl_dist:
                            reverse_sl = reverse_entry - max_sl_dist
                        reverse_tp = reverse_entry + tp_points
                    else:
                        sl_candidate = highest_high
                        reverse_sl = max(reverse_entry + 0.01, min(reverse_entry + max_sl_dist, sl_candidate))
                        # Ensure SL is always above entry for short
                        if reverse_sl - reverse_entry > max_sl_dist:
                            reverse_sl = reverse_entry + max_sl_dist
                        reverse_tp = reverse_entry - tp_points
                    reverse_exit_time = None
                    reverse_exit_price = None
                    reverse_exit_idx = None
                    for k2 in range(exit_idx+1, len(df)):
                        next_candle2 = df.iloc[k2]
                        low2 = next_candle2['low']
                        high2 = next_candle2['high']
                        if reverse_direction == 'Long':
                            if low2 <= reverse_sl and high2 >= reverse_tp:
                                reverse_exit_price = reverse_sl - spread
                                reverse_exit_time = next_candle2['time']
                                reverse_exit_idx = k2
                                break
                            elif high2 >= reverse_tp:
                                reverse_exit_price = reverse_tp - spread
                                reverse_exit_time = next_candle2['time']
                                reverse_exit_idx = k2
                                break
                            elif low2 <= reverse_sl:
                                reverse_exit_price = reverse_sl - spread
                                reverse_exit_time = next_candle2['time']
                                reverse_exit_idx = k2
                                break
                        else:
                            if low2 <= reverse_tp and high2 >= reverse_sl:
                                reverse_exit_price = reverse_sl + spread
                                reverse_exit_time = next_candle2['time']
                                reverse_exit_idx = k2
                                break
                            elif low2 <= reverse_tp:
                                reverse_exit_price = reverse_tp + spread
                                reverse_exit_time = next_candle2['time']
                                reverse_exit_idx = k2
                                break
                            elif high2 >= reverse_sl:
                                reverse_exit_price = reverse_sl + spread
                                reverse_exit_time = next_candle2['time']
                                reverse_exit_idx = k2
                                break
                    if reverse_exit_price is None:
                        if reverse_direction == 'Long':
                            reverse_exit_price = df.iloc[-1]['close'] - spread
                        else:
                            reverse_exit_price = df.iloc[-1]['close'] + spread
                        reverse_exit_time = df.iloc[-1]['time']
                        reverse_exit_idx = len(df) - 1
                    # Calculate PnL correctly for both long and short reverse trades
                    if reverse_direction == 'Long':
                        reverse_pnl = (reverse_exit_price - reverse_entry) * lot_size
                    else:
                        reverse_pnl = (reverse_entry - reverse_exit_price) * lot_size
                    trades.append({
                        'Type': reverse_direction,
                        'Entry Time': exit_time,
                        'Entry Price': reverse_entry,
                        'SL': reverse_sl,
                        'TP': (reverse_entry + tp_points) if reverse_direction == 'Long' else (reverse_entry - tp_points),
                        'Exit Time': reverse_exit_time,
                        'Exit Price': reverse_exit_price,
                        'PnL': reverse_pnl
                    })
                    last_exit_idx = reverse_exit_idx if reverse_exit_idx is not None else exit_idx
                else:
                    last_exit_idx = exit_idx if exit_idx is not None else j
                break  # Only one position at a time
        idx += 1

    if trades:
        print("\nBacktest Trades:")
        print(tabulate(trades, headers='keys', tablefmt='pretty', floatfmt='.2f'))
        # Date-wise PnL summary
        from collections import defaultdict
        date_pnl = defaultdict(float)
        date_profitable = defaultdict(int)
        date_loss = defaultdict(int)
        for t in trades:
            trade_date = pd.to_datetime(t['Exit Time']).date()
            date_pnl[trade_date] += t['PnL']
            if t['PnL'] > 0:
                date_profitable[trade_date] += 1
            elif t['PnL'] < 0:
                date_loss[trade_date] += 1
        date_table = []
        for d in sorted(date_pnl.keys()):
            date_table.append({
                'Date': d,
                'PnL': round(date_pnl[d], 2),
                'Profitable Trades': date_profitable[d],
                'Loss Trades': date_loss[d]
            })
        print("\nDate-wise PnL:")
        print(tabulate(date_table, headers='keys', tablefmt='pretty'))
        net_pnl = sum(t['PnL'] for t in trades)
        profitable = sum(1 for t in trades if t['PnL'] > 0)
        loss = sum(1 for t in trades if t['PnL'] < 0)
        print(f"\nTotal Profitable positions: {profitable}")
        print(f"Total Loss positions: {loss}")
        print(f"\nNet PnL: {net_pnl:.2f}")

        # Max Drawdown Calculation
        equity_curve = []
        running_total = 0
        for t in trades:
            running_total += t['PnL']
            equity_curve.append(running_total)
        max_drawdown = 0
        peak = float('-inf')
        for eq in equity_curve:
            if eq > peak:
                peak = eq
            dd = peak - eq
            if dd > max_drawdown:
                max_drawdown = dd
        print(f"Max Drawdown: {max_drawdown:.2f}")
    else:
        print("No trades triggered in backtest.")

if __name__ == "__main__":
    main()

