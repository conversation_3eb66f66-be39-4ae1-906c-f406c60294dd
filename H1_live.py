
import MetaTrader5 as mt5
import pandas as pd
from tabulate import tabulate



def backtest_cross_high_low(df, lot_size=0.7, spread=0.22):
    trades = []
    position = None  # None, 'long', 'short'
    entry_idx = None
    entry_price = None
    for i in range(1, len(df)):
        prev = df.iloc[i-1]
        curr = df.iloc[i]
        time = df.index[i]
        cross_high = curr['high'] > prev['high']
        cross_low = curr['low'] < prev['low']
        # Realistic backtest: if both crossed, use open, high, low, close sequence to determine which hit first
        if cross_high and cross_low:
            open_ = curr['open']
            high_ = prev['high']
            low_ = prev['low']
            dist_high = abs(high_ - open_)
            dist_low = abs(low_ - open_)
            if dist_high < dist_low:
                # High crossed first
                if position == 'short':
                    exit_price = high_ + spread
                    trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'closed'})
                    position = 'long'
                    entry_idx = i
                    entry_price = high_ + spread
                    trades.append({'entry_time': time, 'direction': 'long', 'entry': entry_price})
                elif position is None:
                    position = 'long'
                    entry_idx = i
                    entry_price = high_ + spread
                    trades.append({'entry_time': time, 'direction': 'long', 'entry': entry_price})
                elif position == 'long':
                    exit_price = low_
                    trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'closed'})
                    position = None
                    entry_idx = None
                    entry_price = None
            else:
                # Low crossed first
                if position == 'long':
                    exit_price = low_
                    trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'closed'})
                    position = 'short'
                    entry_idx = i
                    entry_price = low_
                    trades.append({'entry_time': time, 'direction': 'short', 'entry': entry_price})
                elif position is None:
                    position = 'short'
                    entry_idx = i
                    entry_price = low_
                    trades.append({'entry_time': time, 'direction': 'short', 'entry': entry_price})
                elif position == 'short':
                    exit_price = high_ + spread
                    trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'closed'})
                    position = None
                    entry_idx = None
                    entry_price = None
            continue
        # Exit and immediate reversal logic
        if position == 'long' and cross_low:
            exit_price = prev['low']
            trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'closed'})
            position = 'short'
            entry_idx = i
            entry_price = prev['low']
            trades.append({'entry_time': time, 'direction': 'short', 'entry': entry_price})
        elif position == 'short' and cross_high:
            exit_price = prev['high'] + spread
            trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'closed'})
            position = 'long'
            entry_idx = i
            entry_price = prev['high'] + spread
            trades.append({'entry_time': time, 'direction': 'long', 'entry': entry_price})
        elif position is None:
            if cross_high:
                position = 'long'
                entry_idx = i
                entry_price = prev['high'] + spread
                trades.append({'entry_time': time, 'direction': 'long', 'entry': entry_price})
            elif cross_low:
                position = 'short'
                entry_idx = i
                entry_price = prev['low']
                trades.append({'entry_time': time, 'direction': 'short', 'entry': entry_price})
    # If still open at end, close at last close
    if position is not None:
        last = df.iloc[-1]
        time = df.index[-1]
        if position == 'long':
            exit_price = last['close']  # Long exit at bid
        else:
            exit_price = last['close'] + spread  # Short exit at ask
        trades[-1].update({'exit_time': time, 'exit': exit_price, 'result': 'eod'})
    # Calculate PnL for each trade
    for t in trades:
        if 'exit' in t:
            if t['direction'] == 'long':
                t['pnl'] = (t['exit'] - t['entry']) * lot_size
            elif t['direction'] == 'short':
                t['pnl'] = (t['entry'] - t['exit']) * lot_size
            else:
                t['pnl'] = 0
        else:
            t['pnl'] = 0
    return pd.DataFrame(trades)

def main():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")
    symbol = "Step Index"
    from datetime import datetime, timedelta
    utc_to = datetime.now()
    utc_from = utc_to - timedelta(days=20)
    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_H1, utc_from, utc_to)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No H1 data returned for symbol {symbol} in the given period.")
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    df.set_index('time', inplace=True)
    trades = backtest_cross_high_low(df, lot_size=0.7, spread=0.22)
    print("Backtest Trades:")
    if not trades.empty:
        print(tabulate(trades, headers='keys', tablefmt='fancy_grid', floatfmt='.2f'))
        netpnl = trades['pnl'].sum()
        # Calculate max drawdown (largest drop from any peak, including open position)
        equity = trades['pnl'].cumsum()
        roll_max = equity.cummax()
        drawdown = equity - roll_max
        max_drawdown = drawdown.min() if not drawdown.empty else 0
        # If last trade is open, include its current loss in drawdown
        if not trades.empty and trades.iloc[-1]['result'] == 'eod':
            # Add current open position loss to previous drawdown
            current_loss = trades.iloc[-1]['pnl'] if trades.iloc[-1]['pnl'] < 0 else 0
            max_drawdown = min(max_drawdown, drawdown.min() + current_loss)
        print(f"\nNet PnL: {netpnl:.2f}")
        print(f"Max Drawdown: {max_drawdown:.2f}")
        num_profitable = (trades['pnl'] > 0).sum()
        num_loss = (trades['pnl'] < 0).sum()
        print(f"Profitable Trades: {num_profitable}")
        print(f"Loss Trades: {num_loss}")
    else:
        print("No trades found.")
    mt5.shutdown()

if __name__ == "__main__":
    main()
