


# --- PARAMETERS (from user-provided table) ---
ATR_PERIOD = 8
NUM_BARS_COUNTED = 1
SOUND_ALERT = False
NUM_ALERTS = 2
RECEIVE_ALERTS_MAIL = False
PUSH_NOTIFICATIONS = False

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def compute_atr(df, period=14):
    high = df['high']
    low = df['low']
    close = df['close']
    prev_close = close.shift(1)
    tr1 = high - low
    tr2 = (high - prev_close).abs()
    tr3 = (low - prev_close).abs()
    tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
    atr = tr.rolling(window=period, min_periods=period).mean()
    atr.iloc[:period] = tr.iloc[:period].mean()  # Set first ATR values to mean of available TRs
    return atr

def alert(message, alert_count=[0]):
    if alert_count[0] < NUM_ALERTS:
        print(f"ALERT: {message}")
        if SOUND_ALERT:
            print("[Sound alert would play here]")
        if RECEIVE_ALERTS_MAIL:
            print("[Email alert would be sent here]")
        if PUSH_NOTIFICATIONS:
            print("[Push notification would be sent here]")
        alert_count[0] += 1

def main():
    initialize_mt5()
    symbol = 'Volatility 100 (1s) Index'
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M1, 0, 500)
    if rates is None or len(rates) == 0:
        print(f"No data returned from MT5 for {symbol}.")
        return
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    # Time-delta in minutes
    df['Time_Diff'] = df['time'].diff().dt.total_seconds() / 60.0
    # Price-delta
    df['Price_Diff'] = df['close'] - df['close'].shift(1)
    # Velocity (points per minute)
    df['Velocity'] = df['Price_Diff'] / df['Time_Diff']


    df['ATR'] = compute_atr(df, period=ATR_PERIOD)

    # 1. Rate of Change (ROC) / Velocity (n=1 by default, can be changed)
    ROC_N = 1
    df['ROC'] = (df['close'] - df['close'].shift(ROC_N)) / df['close'].shift(ROC_N) * 100

    # 2. Momentum (n=1 by default, can be changed)
    MOM_N = 1
    df['Momentum'] = df['close'] - df['close'].shift(MOM_N)

    # 3. Acceleration (change in momentum)
    df['Acceleration'] = df['Momentum'] - df['Momentum'].shift(1)
    # Smooth acceleration with a rolling mean to reduce noise
    ACCEL_SMOOTH_WINDOW = 5
    df['Acc_Smooth'] = df['Acceleration'].rolling(window=ACCEL_SMOOTH_WINDOW, min_periods=1).mean()

    # 4. Force Indicator (Volume × Acceleration)
    if 'tick_volume' in df.columns:
        df['Volume'] = df['tick_volume']
    elif 'real_volume' in df.columns:
        df['Volume'] = df['real_volume']
    else:
        df['Volume'] = np.nan
    df['Force'] = df['Volume'] * df['Acceleration']

    # 5. Energy Indicator (½ × Volume × ROC²)
    df['Energy'] = 0.5 * df['Volume'] * (df['ROC'] ** 2)

    # 6. Mass (Liquidity Proxy) - using Volume as proxy
    df['Mass'] = df['Volume']

    # 7. VWAP (Volume Weighted Average Price)
    df['VWAP'] = (df['close'] * df['Volume']).cumsum() / df['Volume'].cumsum()

    # 8. ADX (Trend Strength)
    try:
        import ta
        df['ADX'] = ta.trend.adx(df['high'], df['low'], df['close'], window=14, fillna=True)
    except ImportError:
        df['ADX'] = np.nan

    # 9. MACD (Momentum + Inertia)
    df['EMA12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['EMA26'] = df['close'].ewm(span=26, adjust=False).mean()
    df['MACD'] = df['EMA12'] - df['EMA26']
    df['MACD_Signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
    df['MACD_Hist'] = df['MACD'] - df['MACD_Signal']

    # 10. Bollinger Bands (Mean Reversion / Gravity)
    BB_WINDOW = 20
    BB_STD = 2
    df['BB_Mid'] = df['close'].rolling(window=BB_WINDOW).mean()
    df['BB_Upper'] = df['BB_Mid'] + BB_STD * df['close'].rolling(window=BB_WINDOW).std()
    df['BB_Lower'] = df['BB_Mid'] - BB_STD * df['close'].rolling(window=BB_WINDOW).std()


    # Signal rule (unchanged, but you can enhance with new indicators)
    # For demonstration, keep the previous swing-score/acceleration logic
    THRESHOLD = 0.500
    df['Swing_Score'] = df['Velocity'] / df['ATR']
    df['Swing_Score_Diff'] = df['Swing_Score'] - df['Swing_Score'].shift(1)
    # Require smoothed acceleration to be positive (LONG) or negative (SHORT) for N consecutive bars
    HOLD_BARS = 3
    def signal_rule(row_idx, row):
        s = row['Swing_Score']
        s_prev = s - row['Swing_Score_Diff']
        acc_smooth = row['Acc_Smooth']
        if pd.isna(s_prev) or pd.isna(acc_smooth):
            return 'NO SIGNAL'
        # Check if smoothed acceleration has been positive/negative for HOLD_BARS
        if row_idx >= HOLD_BARS:
            acc_hist = df['Acc_Smooth'].iloc[row_idx-HOLD_BARS+1:row_idx+1]
            if (s > THRESHOLD) and (s > s_prev) and (acc_hist.min() > 0):
                return 'LONG'
            elif (s < -THRESHOLD) and (s < s_prev) and (acc_hist.max() < 0):
                return 'SHORT'
        return 'NO SIGNAL'
    df['Signal'] = [signal_rule(i, row) for i, row in df.iterrows()]


    # Print full table


    # Print full table with all indicators
    print(tabulate(df[['time', 'close', 'ATR', 'ROC', 'Momentum', 'Acceleration', 'Acc_Smooth', 'Force', 'Energy', 'Mass', 'VWAP', 'ADX', 'MACD', 'MACD_Hist', 'BB_Mid', 'BB_Upper', 'BB_Lower', 'Swing_Score', 'Signal']], headers='keys', tablefmt='fancy_grid', showindex=False))

    # Print only LONG/SHORT signals


    signal_df = df[df['Signal'].isin(['LONG', 'SHORT'])]
    if not signal_df.empty:
        print("\nLONG/SHORT Signals Table:")
        print(tabulate(signal_df[['time', 'close', 'ATR', 'ROC', 'Momentum', 'Acceleration', 'Acc_Smooth', 'Force', 'Energy', 'Mass', 'VWAP', 'ADX', 'MACD', 'MACD_Hist', 'BB_Mid', 'BB_Upper', 'BB_Lower', 'Swing_Score', 'Signal']], headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print("\nNo LONG or SHORT signals found.")

    # Store only closing price and ATR in a CSV file
    df[['time', 'close', 'ATR', 'ROC', 'Momentum', 'Acceleration', 'Acc_Smooth', 'Force', 'Energy', 'Mass', 'VWAP', 'ADX', 'MACD', 'MACD_Hist', 'BB_Mid', 'BB_Upper', 'BB_Lower', 'Swing_Score', 'Signal']].to_csv('close_atr_output.csv', index=False)

if __name__ == "__main__":
    main()

