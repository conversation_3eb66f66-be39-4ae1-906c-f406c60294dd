

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta > 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")


def main():
    initialize_mt5()
    symbol = 'Volatility 100 (1s) Index'
    lower_tf_rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M5, 0, 2000)
    if lower_tf_rates is None or len(lower_tf_rates) == 0:
        print(f"No data returned from MT5 for {symbol}.")
        return
    df = pd.DataFrame(lower_tf_rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    close = df['close']
    # 1. Simple Moving Average (SMA)
    df['SMA25'] = close.rolling(window=25, min_periods=1).mean()
    # 2. Exponential Moving Average (EMA)
    df['EMA25'] = close.ewm(span=25, adjust=False).mean()
    # 3. Weighted Moving Average (WMA)
    def wma(series, window):
        weights = np.arange(1, window + 1)
        return series.rolling(window).apply(lambda x: np.dot(x, weights)/weights.sum(), raw=True)
    df['WMA25'] = wma(close, 25)
    # 4. Hull Moving Average (HMA)
    def hma(series, window):
        half = int(window / 2)
        sqrt = int(np.sqrt(window))
        wma_half = wma(series, half)
        wma_full = wma(series, window)
        diff = 2 * wma_half - wma_full
        return wma(diff, sqrt)
    df['HMA25'] = hma(close, 25)
    # 5. Smoothed Moving Average (SMMA)
    def smma(series, window):
        smma = series.copy()
        smma.iloc[:window] = series.iloc[:window].mean()
        for i in range(window, len(series)):
            smma.iloc[i] = (smma.iloc[i-1]*(window-1) + series.iloc[i]) / window
        return smma
    df['SMMA25'] = smma(close, 25)
    # 6. Triangular Moving Average (TMA)
    def tma(series, window):
        return series.rolling(window=window, min_periods=1).mean().rolling(window=window, min_periods=1).mean()
    df['TMA25'] = tma(close, 25)
    # 7. Double Exponential Moving Average (DEMA)
    ema = df['EMA25']
    ema_ema = ema.ewm(span=25, adjust=False).mean()
    df['DEMA25'] = 2 * ema - ema_ema
    # 8. Triple Exponential Moving Average (TEMA)
    ema_ema_ema = ema_ema.ewm(span=25, adjust=False).mean()
    df['TEMA25'] = 3 * ema - 3 * ema_ema + ema_ema_ema
    # 9. Kaufman's Adaptive Moving Average (KAMA)
    try:
        import ta
        # Use the correct function for KAMA in ta library
        if hasattr(ta.trend, 'KAMAIndicator'):
            kama_indicator = ta.trend.KAMAIndicator(close, window=25, pow1=2, pow2=30)
            df['KAMA25'] = kama_indicator.kama()
        else:
            # Fallback: simple KAMA implementation
            def kama(series, window, pow1=2, pow2=30):
                # This is a basic approximation, not the full KAMA
                return series.ewm(span=window, adjust=False).mean()
            df['KAMA25'] = kama(close, 25)
    except ImportError:
        df['KAMA25'] = np.nan
    # 10. Least Squares Moving Average (LSMA)
    def lsma(series, window):
        def linreg(x):
            idx = np.arange(window)
            A = np.vstack([idx, np.ones(window)]).T
            m, c = np.linalg.lstsq(A, x, rcond=None)[0]
            return m * (window - 1) + c
        return series.rolling(window).apply(linreg, raw=True)
    df['LSMA25'] = lsma(close, 25)
    # 11. Volume Weighted Moving Average (VWMA)
    if 'tick_volume' in df.columns:
        df['VWMA25'] = (close * df['tick_volume']).rolling(25).sum() / df['tick_volume'].rolling(25).sum()
    else:
        df['VWMA25'] = np.nan
    # 12. Arnaud Legoux Moving Average (ALMA)
    def alma(series, window, offset=0.85, sigma=6):
        m = offset * (window - 1)
        s = window / sigma
        wts = np.exp(-((np.arange(window) - m) ** 2) / (2 * s * s))
        wts /= wts.sum()
        return series.rolling(window).apply(lambda x: np.dot(x, wts), raw=True)
    df['ALMA25'] = alma(close, 25)
    # 13. Fractal Adaptive Moving Average (FRAMA) - simplified version
    def frama(series, window):
        # This is a simplified version, not the full FRAMA
        return series.ewm(span=window, adjust=False).mean()
    df['FRAMA25'] = frama(close, 25)
    # Weighted average of all 13 MAs (equal weight)
    ma_cols = ['SMA25','EMA25','WMA25','HMA25','SMMA25','TMA25','DEMA25','TEMA25','KAMA25','LSMA25','VWMA25','ALMA25','FRAMA25']
    df['MA_WEIGHTED'] = df[ma_cols].mean(axis=1, skipna=True)
    # Print table with time, close, EMA25, MA_WEIGHTED
    print(tabulate(df[['time', 'close', 'EMA25', 'MA_WEIGHTED']], headers='keys', tablefmt='fancy_grid', showindex=False))

    # Find cross events: MA_WEIGHTED crosses close (above or below)
    cross_rows = []
    for i in range(1, len(df)):
        prev_close = df['close'].iloc[i-1]
        prev_ma = df['MA_WEIGHTED'].iloc[i-1]
        curr_close = df['close'].iloc[i]
        curr_ma = df['MA_WEIGHTED'].iloc[i]
        # Cross above: prev MA below prev close, curr MA above curr close
        if (prev_ma < prev_close) and (curr_ma > curr_close):
            cross_rows.append({
                'time': df['time'].iloc[i],
                'close': curr_close,
                'EMA25': df['EMA25'].iloc[i],
                'MA_WEIGHTED': curr_ma,
                'Cross': 'Above'
            })
        # Cross below: prev MA above prev close, curr MA below curr close
        elif (prev_ma > prev_close) and (curr_ma < curr_close):
            cross_rows.append({
                'time': df['time'].iloc[i],
                'close': curr_close,
                'EMA25': df['EMA25'].iloc[i],
                'MA_WEIGHTED': curr_ma,
                'Cross': 'Below'
            })
    if cross_rows:
        print("\nCross events (MA_WEIGHTED crosses close):")
        print(tabulate(cross_rows, headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print("\nNo cross events found.")

if __name__ == "__main__":
    main()

