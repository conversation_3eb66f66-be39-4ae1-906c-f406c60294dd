
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

SYMBOL       = "Volatility 100 (1s) Index"
TIMEFRAME    = mt5.TIMEFRAME_M1
FETCH_COUNT  = 20000

# Initialize MT5
if not mt5.initialize():
    raise RuntimeError(f"MT5 initialization failed: {mt5.last_error()}")

def fetch_data(symbol, timeframe, count):
    rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
    if rates is None or len(rates) == 0:
        raise ValueError(f"No data returned for symbol {symbol}.")
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')
    return df.set_index('time')




def nadaraya_watson_envelope(src, h=8.0, mult=3.0, window=50):
    n = len(src)
    coefs = np.array([np.exp(-(i ** 2) / (h * h * 2)) for i in range(window)])
    coefs = coefs[::-1]
    den = np.sum(coefs)
    out = np.full(n, np.nan)
    for i in range(window, n):
        values = src[i-window:i]
        out[i] = np.sum(values * coefs) / den
    # Only compute MAE for valid region
    abs_err = np.abs(src - out)
    mae = np.full(n, np.nan)
    # Only fill MAE where out is not nan
    valid_idx = np.where(~np.isnan(out))[0]
    if len(valid_idx) > 0:
        # Compute rolling mean only for valid region
        abs_err_valid = abs_err[valid_idx]
        mae_valid = pd.Series(abs_err_valid).rolling(window, min_periods=1).mean().values * mult
        mae[valid_idx] = mae_valid
    upper = out + mae
    lower = out - mae
    return out, upper, lower

if __name__ == "__main__":
    df = fetch_data(SYMBOL, TIMEFRAME, FETCH_COUNT)
    close = df['close'].values
    mean, upper, lower = nadaraya_watson_envelope(close, h=9.0, mult=3.7, window=30)
    df['NW_mean'] = mean
    df['NW_upper'] = upper
    df['NW_lower'] = lower
    valid = (~np.isnan(df['NW_upper'])) & (~np.isnan(df['NW_lower']))
    signals = []
    for i in range(len(df)):
        if not valid[i]:
            signals.append(None)
        elif df['close'].iloc[i] > df['NW_upper'].iloc[i]:
            signals.append('short')
        elif df['close'].iloc[i] < df['NW_lower'].iloc[i]:
            signals.append('long')
        else:
            signals.append(None)
    # Trading simulation parameters
    base_lot_size = 0.7
    lot_size = base_lot_size
    spread = 0.28
    target_pnl = 1.5
    cycles = []
    i = 0
    n = len(df)
    while i < n:
        # Wait for a signal to start a new cycle
        if signals[i] is None:
            i += 1
            continue
        # Start new cycle
        cycle_pnl = 0.0
        max_drawdown = 0.0
        running_pnl = 0.0
        trade_history = []
        trade_count = 0
        # For drawdown tracking
        peak = 0.0
        trough = 0.0
        # Open initial position
        entry_idx = i
        direction = signals[i]
        pos = 1 if direction == 'long' else -1
        lot_size = base_lot_size  # Reset lot size at start of each cycle
        entry_price = df['close'].iloc[i] + (spread if direction == 'short' else -spread)
        open_trade = {
            'entry_time': df.index[entry_idx],
            'direction': 'long' if pos == 1 else 'short',
            'entry': entry_price,
            'lot_size': lot_size
        }
        i += 1
        while i < n:
            # Calculate current PnL for open position
            price_now = df['close'].iloc[i] - (spread if pos == 1 else -spread)
            trade_pnl = (price_now - entry_price) * pos * lot_size
            running_pnl = cycle_pnl + trade_pnl
            # Drawdown tracking
            if running_pnl > peak:
                peak = running_pnl
                trough = running_pnl
            elif running_pnl < trough:
                trough = running_pnl
            max_drawdown = min(max_drawdown, trough - peak)
            # Check for cycle close
            if running_pnl >= target_pnl:
                # Close position and cycle
                exit_price = df['close'].iloc[i] - (spread if pos == 1 else -spread)
                realized_pnl = (exit_price - entry_price) * pos * lot_size
                trade_count += 1
                open_trade['exit_time'] = df.index[i]
                open_trade['exit'] = exit_price
                open_trade['pnl'] = realized_pnl
                trade_history.append(open_trade)
                cycle_pnl += realized_pnl
                break
            # Check for reversal (loss of -1.5 or worse)
            if trade_pnl <= -target_pnl:
                # Close current position
                exit_price = df['close'].iloc[i] - (spread if pos == 1 else -spread)
                realized_pnl = (exit_price - entry_price) * pos * lot_size
                trade_count += 1
                open_trade['exit_time'] = df.index[i]
                open_trade['exit'] = exit_price
                open_trade['pnl'] = realized_pnl
                trade_history.append(open_trade)
                cycle_pnl += realized_pnl
                # Reverse position immediately, double lot size
                pos = -pos
                direction = 'long' if pos == 1 else 'short'
                lot_size *= 2
                entry_price = df['close'].iloc[i] + (spread if direction == 'short' else -spread)
                open_trade = {
                    'entry_time': df.index[i],
                    'direction': direction,
                    'entry': entry_price,
                    'lot_size': lot_size
                }
                # Continue in same bar with new position
                continue
            i += 1
        # End of cycle
        cycles.append({
            'trades': trade_history,
            'cycle_pnl': cycle_pnl,
            'max_drawdown': abs(max_drawdown),
            'trade_count': trade_count
        })
    # Print cycle details
    print("\nCycle Details:")
    cycle_table = []
    for idx, c in enumerate(cycles):
        cycle_table.append([
            idx+1,
            c['trade_count'],
            f"{c['max_drawdown']:.2f}",
            f"{c['cycle_pnl']:.2f}"
        ])
    print(tabulate(cycle_table, headers=['Cycle', 'Num Trades', 'Max Drawdown', 'Net Profit'], tablefmt='fancy_grid'))
    # Print summary
    if cycles:
        total_cycles = len(cycles)
        highest_drawdown = max(c['max_drawdown'] for c in cycles)
        total_profit = sum(c['cycle_pnl'] for c in cycles)
        print(f"\nTotal cycles completed: {total_cycles}")
        print(f"Highest max drawdown: {highest_drawdown:.2f}")
        print(f"Total net profit: {total_profit:.2f}")
        # Print detailed trades for each cycle
        for idx, c in enumerate(cycles):
            print(f"\nCycle {idx+1} - Trade Details:")
            if not c['trades']:
                print("No trades in this cycle.")
                continue
            trade_table = []
            for t in c['trades']:
                trade_table.append([
                    t['entry_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    t['exit_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    t['direction'],
                    f"{t['entry']:.5f}",
                    f"{t['exit']:.5f}",
                    f"{t['pnl']:.2f}"
                ])
            print(tabulate(trade_table, headers=['Entry Time', 'Exit Time', 'Direction', 'Entry', 'Exit', 'PnL'], tablefmt='fancy_grid'))
    else:
        print("No cycles completed.")
    mt5.shutdown()
