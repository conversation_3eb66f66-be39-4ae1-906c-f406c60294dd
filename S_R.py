

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from tabulate import tabulate

def calculate_rsi(price_series, rsi_period=14):
    price_delta = price_series.diff()
    gain = price_delta.where(price_delta > 0, 0)
    loss = -price_delta.where(price_delta > 0, 0)
    avg_gain = gain.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    avg_loss = loss.ewm(alpha=1/rsi_period, min_periods=rsi_period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def initialize_mt5():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")

def gauss(x, h):
    return np.exp(-(x ** 2) / (h * h * 2))

def nadaraya_watson_envelope(src, h=8.0, mult=3.0, window=500):
    n = len(src)
    # Precompute Gaussian weights for endpoint method
    coefs = np.array([np.exp(-(i ** 2) / (h * h * 2)) for i in range(window)])
    coefs = coefs[::-1]  # reverse to match src[-window:]
    den = np.sum(coefs)
    out = np.full(n, np.nan)
    for i in range(window, n):
        values = src[i-window:i]
        out[i] = np.sum(values * coefs) / den
    # MAE as rolling mean of abs(src - out)
    mae = pd.Series(np.abs(src - out)).rolling(window, min_periods=1).mean() * mult
    upper = out + mae
    lower = out - mae
    return out, upper, lower

def main():
    if not mt5.initialize():
        raise RuntimeError(f"MT5 initialize() failed, code={mt5.last_error()}")
    print("MetaTrader5 initialized")
    symbol = 'Volatility 100 (1s) Index'
    rates = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_M3, 0, 2000)
    if rates is None or len(rates) == 0:
        print(f"No data returned from MT5 for {symbol}.")
        return
    df = pd.DataFrame(rates)
    df['time'] = pd.to_datetime(df['time'], unit='s')

    supply_demand_records = []
    for period in range(15, 51):
        for i in range(period, len(df) - period):
            window = df.iloc[i - period:i + period + 1]
            center_idx = i
            # Supply: highest high at center
            if df['high'].iloc[center_idx] == window['high'].max():
                supply_demand_records.append({
                    'Type': 'Supply',
                    'Period': period,
                    'Time': df['time'].iloc[center_idx],
                    'Price': df['high'].iloc[center_idx]
                })
            # Demand: lowest low at center
            if df['low'].iloc[center_idx] == window['low'].min():
                supply_demand_records.append({
                    'Type': 'Demand',
                    'Period': period,
                    'Time': df['time'].iloc[center_idx],
                    'Price': df['low'].iloc[center_idx]
                })
    if supply_demand_records:
        result_df = pd.DataFrame(supply_demand_records)
        print(tabulate(result_df, headers='keys', tablefmt='fancy_grid', showindex=False))
    else:
        print("No supply or demand levels found.")

if __name__ == "__main__":
    main()

